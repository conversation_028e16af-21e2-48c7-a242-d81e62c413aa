import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { PointDto } from '../types';
import { usePointData } from '../hooks';
import PointForm from '../components/forms/PointForm';
import PointDetailModal from '../components/forms/PointDetailModal';
import { formatCurrency } from '@/shared/utils/format';

/**
 * Trang quản lý gói rpointAdmin
 */
const PointListPage: React.FC = () => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm, formData, setFormData } = useSlideForm<PointDto | null>();

  // State cho modal xem chi tiết
  const [selectedPointId, setSelectedPointId] = useState<number | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Lấy hook từ usePointData
  const { usePoints, useDeletePoint, usePointDetail } = usePointData();

  // Mutation xóa gói point
  const deletePointMutation = useDeletePoint();

  // Sử dụng hook để lấy chi tiết point
  const { data: pointDetail, isLoading: isDetailLoading } = usePointDetail(selectedPointId || 0);

  // Xử lý xem chi tiết
  const handleView = useCallback((point: PointDto) => {
    setSelectedPointId(point.id);
    setIsDetailModalOpen(true);
  }, []);

  // Xử lý đóng modal chi tiết
  const handleCloseDetailModal = useCallback(() => {
    setIsDetailModalOpen(false);
    setSelectedPointId(null);
  }, []);



  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (point: PointDto) => {
      setFormData(point);
      showForm();
    },
    [setFormData, showForm]
  );

  // Xử lý xóa
  const handleDelete = useCallback(
    (id: number) => {
      if (window.confirm(t('common:confirmDelete'))) {
        deletePointMutation.mutate(id);
      }
    },
    [t, deletePointMutation]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<PointDto>[]>(
    () => [
      {
        key: 'id',
        title: t('rpointAdmin:points.table.id'),
        dataIndex: 'id',
        width: '5%',
        sortable: true,
      },
      {
        key: 'name',
        title: t('rpointAdmin:points.table.name'),
        dataIndex: 'name',
        width: '15%',
        sortable: true,
      },
      {
        key: 'cash',
        title: t('rpointAdmin:points.table.cash'),
        dataIndex: 'cash',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{formatCurrency(value as number)} VND</div>;
        },
      },
      {
        key: 'rate',
        title: t('rpointAdmin:points.table.rate'),
        dataIndex: 'rate',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">1:{value as number}</div>;
        },
      },
      {
        key: 'point',
        title: t('rpointAdmin:points.table.point'),
        dataIndex: 'point',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center font-bold text-primary">{value as number}</div>;
        },
      },
      {
        key: 'isCustomize',
        title: t('rpointAdmin:points.table.isCustomize'),
        dataIndex: 'isCustomize',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const isCustomize = value as boolean;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                isCustomize
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                  : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              }`}
            >
              {isCustomize
                ? t('rpointAdmin:points.filter.customize')
                : t('rpointAdmin:points.filter.fixed')}
            </div>
          );
        },
      },
      {
        key: 'description',
        title: t('rpointAdmin:points.table.description'),
        dataIndex: 'description',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="truncate">{value as string}</div>;
        },
      },
      {
        key: 'actions',
        title: t('rpointAdmin:points.table.actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: PointDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem chi tiết'),
              icon: 'eye',
              onClick: () => handleView(record),
            },
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleDelete(record.id),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t, handleDelete, handleEdit, handleView]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'customize',
        label: t('rpointAdmin:points.filter.customize'),
        icon: 'settings',
        value: true,
      },
      {
        id: 'fixed',
        label: t('rpointAdmin:points.filter.fixed'),
        icon: 'package',
        value: false,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    const queryParams: Record<string, unknown> = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.isCustomize = params.filterValue;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách gói point với queryParams từ dataTable
  const { data: pointData, isLoading } = usePoints(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    setFormData(null);
    showForm();
  };



  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      true: t('rpointAdmin:points.filter.customize'),
      false: t('rpointAdmin:points.filter.fixed'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        <PointForm onCancel={handleCancel} initialData={formData} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={pointData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: pointData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: pointData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xem chi tiết */}
      <PointDetailModal
        isOpen={isDetailModalOpen}
        onClose={handleCloseDetailModal}
        pointDetail={pointDetail}
        isLoading={isDetailLoading}
      />
    </div>
  );
};

export default PointListPage;
