import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý tích hợp cho Admin
 */
const UserIntegrationManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common', 'integration']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Email Server Card */}
        <ModuleCard
          title={t('admin:integration.email.title', 'Quản lý Email ')}
          description={t(
            'admin:integration.email.description',
            'Quản lý cấu hình m<PERSON> chủ email cho hệ thống gửi email tự động'
          )}
          icon="mail"
          linkTo="/integrations/email"
        />
        {/* Facebook Card */}
        <ModuleCard
          title={t('admin:integration.facebook.title', 'Quản lý Facebook')}
          description={t('admin:integration.facebook.description', 'Quản lý tích hợp với Facebook')}
          icon="facebook"
          linkTo="/integrations/facebook"
        />
        {/* Website Card */}
        <ModuleCard
          title={t('admin:integration.website.title', 'Quản lý Website')}
          description={t(
            'admin:integration.website.description',
            'Quản lý tích hợp với các trang web'
          )}
          icon="website"
          linkTo="/integrations/website"
        />

        {/* Banks Card */}
        <ModuleCard
          title={t('integration:bankAccounts.title', 'Quản lý Tài khoản ngân hàng')}
          description={t(
            'integration:bankAccount.description',
            'Quản lý tích hợp với các tài khoản ngân hàng'
          )}
          icon="bank"
          linkTo="/user/bank-accounts"
        />

        {/* SMS Integration Card */}
        <ModuleCard
          title={t('integration:sms.title', 'Quản lý SMS')}
          description={t(
            'integration:sms.description',
            'Quản lý tích hợp các nhà cung cấp dịch vụ SMS'
          )}
          icon="mail-plus"
          linkTo="/integrations/sms"
        />

        {/* Database Integration Card */}
        <ModuleCard
          title={t('admin:integration.database.title', 'Quản lý Database Integration')}
          description={t(
            'admin:integration.database.description',
            'Quản lý kết nối database cho hệ thống tích hợp dữ liệu'
          )}
          icon="database"
          linkTo="/integrations/database"
        />

        {/* Provider Model Card */}
        <ModuleCard
          title={t('integration:providerModel.title', 'Quản lý Provider Model')}
          description={t(
            'integration:ai.description',
            'Quản lý các nhà cung cấp AI và cấu hình kết nối'
          )}
          icon="robot"
          linkTo="/user/provider-model"
        />

        {/* Google Calendar Integration Card */}
        <ModuleCard
          title={t('integration:calendar.title', 'Quản lý Google Calendar')}
          description={t(
            'integration:calendar.description',
            'Tích hợp và đồng bộ lịch với Google Calendar'
          )}
          icon="calendar"
          linkTo="/integrations/google-calendar"
        />

        {/* Shipping Integration Card */}
        <ModuleCard
          title={t('integration:shipping.title', 'Quản lý Vận chuyển')}
          description={t(
            'integration:shipping.description',
            'Tích hợp với các nhà vận chuyển GHN, GHTK, Viettel Post'
          )}
          icon="truck"
          linkTo="/integrations/shipping"
        />

        {/* Cloud Storage Integration Card */}
        <ModuleCard
          title={t('integration:cloudStorage.title', 'Quản lý Cloud Storage')}
          description={t(
            'integration:cloudStorage.description',
            'Tích hợp với Google Drive, OneDrive, Dropbox'
          )}
          icon="cloud"
          linkTo="/integrations/cloud-storage"
        />

        {/* Enterprise Storage Integration Card */}
        <ModuleCard
          title={t('integration:enterpriseStorage.title', 'Quản lý Enterprise Storage')}
          description={t(
            'integration:enterpriseStorage.description',
            'Tích hợp với AWS S3, Azure Blob Storage'
          )}
          icon="server"
          linkTo="/integrations/enterprise-storage"
        />

        {/* External Agents Integration Card */}
        <ModuleCard
          title={t('integration:externalAgents.title', 'Quản lý External Agents')}
          description={t(
            'integration:externalAgents.description',
            'Tích hợp với các external agents thông qua MCP, REST API, WebSocket'
          )}
          icon="users"
          linkTo="/integrations/external-agents"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default UserIntegrationManagementPage;
