import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { FileImage, FileText, Link, Database, HardDrive } from 'lucide-react';

import { ModuleCard } from '@/modules/components/card';
import { Card, Typography, Progress } from '@/shared/components/common';
import { ResponsiveGrid } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import { useDataOverview, useStorageInfo } from '../hooks/useDataOverview';
import StorageSlider from '../components/StorageSlider';

/**
 * Trang tổng quan quản lý dữ liệu
 */
const DataManagementPage: React.FC = () => {
  const { t } = useTranslation(['data']);
  const navigate = useNavigate();

  // Fetch data overview từ API
  const { data: overviewData, isLoading: isOverviewLoading } = useDataOverview();
  const { data: storageData } = useStorageInfo();

  // Overview statistics với data từ API hoặc fallback
  const overviewStats: OverviewCardProps[] = useMemo(() => [
    {
      title: t('data:overview.stats.totalMedia', 'Tổng Media Files'),
      value: overviewData?.totalMediaFiles || 1250,
      description: t('data:overview.stats.mediaDescription', '+15 files mới'),
      icon: FileImage,
      color: 'blue',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalKnowledge', 'File Tri Thức'),
      value: overviewData?.totalKnowledgeFiles || 340,
      description: t('data:overview.stats.knowledgeDescription', '+8 files mới'),
      icon: FileText,
      color: 'green',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalUrls', 'Tổng URLs'),
      value: overviewData?.totalUrls || 89,
      description: t('data:overview.stats.urlDescription', '+3 URLs mới'),
      icon: Link,
      color: 'orange',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalVectorStores', 'Vector Stores'),
      value: overviewData?.totalVectorStores || 12,
      description: overviewData?.storageUsed || t('data:overview.stats.vectorDescription', '2.4 GB sử dụng'),
      icon: Database,
      color: 'purple',
      isLoading: isOverviewLoading,
    },
  ], [t, overviewData, isOverviewLoading]);

  // Handle purchase storage
  const handlePurchaseStorage = (storageAmount: number, price: number) => {
    // TODO: Implement actual purchase logic
    console.log('Purchasing storage:', { storageAmount, price });
    // Navigate to payment page or show payment modal
    navigate(`/subscription/purchase?storage=${storageAmount}&price=${price}&type=storage`);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Overview Statistics */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={isOverviewLoading}
        skeletonCount={4}
      />

      {/* Storage Management */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <HardDrive className="h-6 w-6 text-primary" />
            <Typography variant="h5" className="font-semibold">
              {t('data:storage.title', 'Quản Lý Dung Lượng')}
            </Typography>
          </div>
        </div>

        {/* Current Storage Usage */}
        <div className="mb-6 p-4 bg-muted/30 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <Typography variant="subtitle1" className="font-medium">
              {t('data:storage.currentUsage', 'Dung lượng hiện tại')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {storageData?.usedFormatted || '2.4 GB'} / {storageData?.totalFormatted || '10 GB'}
            </Typography>
          </div>
          <Progress
            value={storageData?.percentage || 24}
            className="h-2 mb-2"
          />
          <Typography variant="caption" className="text-muted-foreground">
            {t('data:storage.remaining', 'Còn lại')}: {storageData?.remainingFormatted || '7.6 GB'}
          </Typography>
        </div>

        {/* Storage Slider */}
        <StorageSlider
          currentStorage={storageData?.total || 10}
          onPurchase={handlePurchaseStorage}
        />
      </Card>

      {/* Module Navigation */}
      <Card className="p-6">
        <Typography variant="h5" className="mb-4 font-semibold">
          {t('data:modules.title', 'Quản Lý Dữ Liệu')}
        </Typography>
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
          gap={6}
        >
          {/* Media Card */}
          <ModuleCard
            title={t('data:media.title', 'Thư viện Media')}
            description={t(
              'data:media.description',
              'Quản lý các tệp tin media như hình ảnh, video, âm thanh và tài liệu.'
            )}
            icon="file-media"
            linkTo="/data/media"
          />

          {/* Knowledge Files Card */}
          <ModuleCard
            title={t('data:knowledgeFiles.title', 'File tri thức')}
            description={t(
              'data:knowledgeFiles.description',
              'Quản lý các tệp tin tri thức được sử dụng cho AI và vector store.'
            )}
            icon="file-text"
            linkTo="/data/knowledge-files"
          />

          {/* URL Card */}
          <ModuleCard
            title={t('data:url.title', 'Quản lý URL')}
            description={t(
              'data:url.description',
              'Quản lý các URL và tài nguyên web được sử dụng trong hệ thống.'
            )}
            icon="link"
            linkTo="/data/url"
          />

          {/* Vector Store Card */}
          <ModuleCard
            title={t('data:vectorStore.title', 'Vector Store')}
            description={t(
              'data:vectorStore.description',
              'Quản lý các vector store và embedding cho các ứng dụng AI và tìm kiếm ngữ nghĩa.'
            )}
            icon="server"
            linkTo="/data/vector-store"
          />
        </ResponsiveGrid>
      </Card>
    </div>
  );
};

export default DataManagementPage;
