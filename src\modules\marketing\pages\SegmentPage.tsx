import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  ActionMenu,
  ActionMenuItem,
  ConfirmDeleteModal,
  Chip,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SegmentForm from '../components/forms/SegmentForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  SegmentStatus,
  SegmentQueryParams,
  Segment,
  CreateSegmentRequest,
  UpdateSegmentRequest,
} from '../types/segment.types';
import { SegmentFormValues } from '../components/forms/SegmentForm';
import { useSegments, useCreateSegment, useDeleteSegment, useUpdateSegment } from '../hooks';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang quản lý phân đoạn sử dụng các hooks tối ưu
 */
const SegmentPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [segmentToDelete, setSegmentToDelete] = useState<Segment | null>(null);

  // State cho chọn nhiều và xóa nhiều
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho chỉnh sửa
  const [editingSegment, setEditingSegment] = useState<Segment | null>(null);
  const [showEditForm, setShowEditForm] = useState(false);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((segment: Segment) => {
    setSegmentToDelete(segment);
    setShowDeleteConfirm(true);
  }, []);

  // Hooks để gọi API
  const { mutateAsync: deleteSegment } = useDeleteSegment();
  const { mutateAsync: createSegment } = useCreateSegment();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((segment: Segment) => {
    setEditingSegment(segment);
    setShowEditForm(true);
  }, []);

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    setEditingSegment(null);
    setShowEditForm(false);
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo(
    () => [
      {
        key: 'id',
        title: t('common:id', 'ID'),
        dataIndex: 'id',
        width: '80px',
        sortable: true
      },
      {
        key: 'name',
        title: t('marketing:segment.name', 'Tên phân đoạn'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            <span>{String(value || '')}</span>
          </div>
        ),
      },
      {
        key: 'description',
        title: t('marketing:segment.description', 'Mô tả'),
        dataIndex: 'description',
        sortable: true,
      },
      {
        key: 'totalContacts',
        title: t('marketing:segment.totalContacts', 'Số liên hệ'),
        dataIndex: 'totalContacts',
        sortable: true,
        render: (value: unknown) => (
          <div className="text-center font-medium">{value as number}</div>
        ),
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        render: (_: unknown, record: Segment) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm thao tác')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                showAllInMenu={false}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t, handleEdit, handleShowDeleteConfirm]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: SegmentStatus.ACTIVE },
      { id: 'draft', label: t('common:draft'), icon: 'file', value: SegmentStatus.DRAFT },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: SegmentStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): SegmentQueryParams => {
      const queryParams: SegmentQueryParams = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as SegmentStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<Segment, SegmentQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: segmentData, isLoading: isLoadingSegments } = useSegments(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (segmentData) {
      // Sử dụng tham chiếu để tránh vòng lặp vô hạn
      updateTableDataRef.current(segmentData, isLoadingSegments);
    }
  }, [segmentData, isLoadingSegments]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        [SegmentStatus.ACTIVE]: t('common:active', 'Hoạt động'),
        [SegmentStatus.DRAFT]: t('common:draft', 'Bản nháp'),
        [SegmentStatus.INACTIVE]: t('common:inactive', 'Không hoạt động'),
      },
      t,
    });

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setSegmentToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!segmentToDelete) return;

    try {
      // Gọi API xóa segment
      await deleteSegment(segmentToDelete.id);

      // Đóng popup
      setShowDeleteConfirm(false);
      setSegmentToDelete(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:segment.deleteSuccess', 'Đã xóa phân đoạn thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting segment:', error);
      NotificationUtil.error({
        message: t('marketing:segment.deleteError', 'Có lỗi xảy ra khi xóa phân đoạn'),
        duration: 3000,
      });
    }
  }, [segmentToDelete, deleteSegment, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    // Kiểm tra cả số lượng mục đã chọn và dữ liệu có tồn tại không
    if (selectedRowKeys.length === 0 || (segmentData?.items?.length ?? 0) === 0) {
      NotificationUtil.warning({
        message: t('marketing:segment.selectSegmentsToDelete', 'Vui lòng chọn ít nhất một phân đoạn để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, segmentData?.items?.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Lưu số lượng segment đã chọn trước khi xóa để hiển thị thông báo
      const deletedCount = selectedRowKeys.length;

      // Gọi API xóa nhiều segment cùng lúc
      await Promise.all(selectedRowKeys.map(id => deleteSegment(Number(id))));

      // Đảm bảo đặt lại selectedRowKeys thành mảng rỗng trước khi đóng modal
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t(
          'marketing:segment.bulkDeleteSuccess',
          `Đã xóa ${deletedCount} phân đoạn thành công`
        ),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting segments:', error);
      NotificationUtil.error({
        message: t('marketing:segment.bulkDeleteError', 'Có lỗi xảy ra khi xóa phân đoạn'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteSegment, t]);

  // Xử lý submit form tạo segment mới
  const handleSubmitCreateSegment = async (values: SegmentFormValues) => {
    try {
      console.log('Form values:', values);

      // Chuẩn bị dữ liệu theo định dạng yêu cầu của API
      const segmentData: CreateSegmentRequest = {
        name: values.name,
        description: values.description,
        audienceId: 1, // Tạm thời hardcode, sẽ cần thêm audience selection
        status: SegmentStatus.DRAFT,
        groups: values.groups || [],
      };

      // Tạo segment
      await createSegment(segmentData);

      // Đóng form
      hideForm();

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:segment.createSuccess', 'Đã tạo phân đoạn thành công'),
        duration: 3000,
      });
    } catch (err) {
      console.error('Create segment error:', err);
      NotificationUtil.error({
        message: t('marketing:segment.createError', 'Có lỗi xảy ra khi tạo phân đoạn. Vui lòng thử lại sau.'),
        duration: 5000,
      });
    }
  };

  // Hook để cập nhật segment (chỉ tạo khi có editingSegment)
  const { mutateAsync: updateSegment } = useUpdateSegment(editingSegment?.id || 0);

  // Xử lý submit form cập nhật segment
  const handleSubmitUpdateSegment = async (values: SegmentFormValues) => {
    if (!editingSegment) return;

    try {
      console.log('Update values:', values);

      // Chuẩn bị dữ liệu theo định dạng yêu cầu của API
      const updateData: UpdateSegmentRequest = {
        name: values.name,
        description: values.description,
        groups: values.groups || [],
      };

      // Cập nhật segment
      await updateSegment(updateData);

      // Đóng form
      handleCloseEditForm();

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:segment.updateSuccess', 'Đã cập nhật phân đoạn thành công'),
        duration: 3000,
      });
    } catch (err) {
      console.error('Update segment error:', err);
      NotificationUtil.error({
        message: t('marketing:segment.updateError', 'Có lỗi xảy ra khi cập nhật phân đoạn. Vui lòng thử lại sau.'),
        duration: 5000,
      });
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={dataTable.tableData.handleSearch}
            onAdd={() => showForm()}
            items={dataTable.menuItems}
            onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
            columns={dataTable.columnVisibility.visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common:bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: () => {
                  // Kiểm tra lại số lượng mục đã chọn và dữ liệu có tồn tại không
                  if (selectedRowKeys.length > 0 && (segmentData?.items?.length ?? 0) > 0) {
                    handleShowBulkDeleteConfirm();
                  } else {
                    NotificationUtil.info({
                      message: t('marketing:segment.selectSegmentsToDelete', 'Vui lòng chọn ít nhất một phân đoạn để xóa'),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-red-500',
                // Chỉ hiển thị khi có dữ liệu và có mục được chọn
                condition: selectedRowKeys.length > 0 && (segmentData?.items?.length ?? 0) > 0,
              }
            ]}
          />
        </div>

        {/* Hiển thị ActiveFilters */}
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isVisible}>
          <SegmentForm
            onSubmit={handleSubmitCreateSegment}
            onCancel={hideForm}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={showEditForm}>
          <SegmentForm
            initialData={editingSegment}
            onSubmit={handleSubmitUpdateSegment}
            onCancel={handleCloseEditForm}
          />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<Segment>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={segmentData?.items || []}
            rowKey="id"
            loading={isLoadingSegments}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: segmentData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: segmentData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:segment.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa phân đoạn này?')}
        itemName={segmentToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={
          selectedRowKeys.length > 0
            ? t(
                'marketing:segment.confirmBulkDeleteMessage',
                `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} phân đoạn đã chọn?`
              )
            : t('marketing:segment.noSegmentsSelected', 'Không có phân đoạn nào được chọn')
        }
      />
    </div>
  );
};

export default SegmentPage;
