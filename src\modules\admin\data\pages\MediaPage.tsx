import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  ConfirmDeleteModal,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';

// Import hooks từ module media
import {
  useAdminMediaList,
  useDeleteMultipleMedia,
} from '@/modules/admin/data/media/hooks/useMedia';

// Import types từ module media
import { AdminMediaDto, MediaQueryDto } from '@/modules/admin/data/media/types/media.types';
import { MediaStatusEnum } from '@/modules/data/media/types/media.types';
import { formatDate } from '@/shared/utils/format';
import { MediaDetailView } from '../components';

/**
 * Trang quản lý media trong admin
 */
const MediaPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<AdminMediaDto | null>(null);

  // State cho chọn nhiều và xóa nhiều
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho hiển thị media detail
  const [mediaToView, setMediaToView] = useState<AdminMediaDto | null>(null);

  // Sử dụng hook animation cho form xem chi tiết
  const {
    isVisible: isViewFormVisible,
    showForm: showViewForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  // Xử lý hiển thị media detail
  const handleShowMedia = useCallback(
    (media: AdminMediaDto) => {
      setMediaToView(media);
      showViewForm();
    },
    [showViewForm]
  );

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setMediaToView(null);
  }, [hideViewForm]);

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((media: AdminMediaDto) => {
    setFileToDelete(media);
    setShowDeleteConfirm(true);
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo(
    () => [
      {
        key: 'name',
        title: t('admin:data.media.table.name', 'Tên'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown) => (
          <div className="flex items-center">
            <span>{String(value || '')}</span>
          </div>
        ),
      },
      {
        key: 'description',
        title: t('admin:data.media.table.description', 'Mô tả'),
        dataIndex: 'description',
        sortable: true,
      },
      {
        key: 'size',
        title: t('admin:data.media.table.size', 'Kích thước'),
        dataIndex: 'size',
        sortable: true,
        render: (value: unknown) => {
          const size = value as number;
          if (size < 1024) {
            return `${size} B`;
          } else if (size < 1024 * 1024) {
            return `${(size / 1024).toFixed(2)} KB`;
          } else if (size < 1024 * 1024 * 1024) {
            return `${(size / (1024 * 1024)).toFixed(2)} MB`;
          } else {
            return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
          }
        },
      },
      {
        key: 'author',
        title: t('admin:data.media.table.author', 'Người tạo'),
        dataIndex: 'author',
        render: (value: unknown, record: AdminMediaDto) => (
          <div className="flex flex-col">
            <span>{value as string}</span>
            <span className="text-xs text-gray-500">ID: {record.ownedBy}</span>
          </div>
        ),
      },
      {
        key: 'createdAt',
        title: t('admin:data.media.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'status',
        title: t('admin:data.media.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (_: unknown, record: AdminMediaDto) => (
          <div className="flex items-center">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                record.status === MediaStatusEnum.APPROVED
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}
            >
              {record.status === MediaStatusEnum.APPROVED
                ? t('common.status.active', 'Hoạt động')
                : t('common.status.pending', 'Chờ duyệt')}
            </span>
          </div>
        ),
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: AdminMediaDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common.view', 'Xem'),
              icon: 'eye',
              onClick: () => handleShowMedia(record),
            },
            {
              id: 'delete',
              label: t('common.delete', 'Xóa'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t, handleShowMedia, handleShowDeleteConfirm]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'approved',
        label: t('common.status.active', 'Hoạt động'),
        icon: 'check',
        value: MediaStatusEnum.APPROVED,
      },
      {
        id: 'pending',
        label: t('common.status.pending', 'Chờ duyệt'),
        icon: 'eye-off',
        value: MediaStatusEnum.PENDING,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): MediaQueryDto => {
      const queryParams: MediaQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as MediaStatusEnum;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AdminMediaDto, MediaQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: mediaData, isLoading: isLoadingMedia } = useAdminMediaList(dataTable.queryParams);

  // Sử dụng hook để xóa media

  const { mutateAsync: deleteMultipleMedia } = useDeleteMultipleMedia();

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (mediaData) {
      // Sử dụng tham chiếu để tránh vòng lặp vô hạn
      updateTableDataRef.current(mediaData, isLoadingMedia);
    }
  }, [mediaData, isLoadingMedia]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        [MediaStatusEnum.APPROVED]: t('common.status.active', 'Hoạt động'),
        [MediaStatusEnum.PENDING]: t('common.status.pending', 'Chờ duyệt'),
      },
      t,
    });

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      // Gọi API xóa file
      await deleteMultipleMedia([fileToDelete.id]);

      // Đóng popup
      setShowDeleteConfirm(false);
      setFileToDelete(null);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('admin:data.media.deleteSuccess', 'Đã xóa media thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting media:', error);
      NotificationUtil.error({
        message: t('admin:data.media.deleteError', 'Có lỗi xảy ra khi xóa media'),
        duration: 3000,
      });
    }
  }, [fileToDelete, deleteMultipleMedia, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    // Kiểm tra cả số lượng mục đã chọn và dữ liệu có tồn tại không
    if (selectedRowKeys.length === 0 || (mediaData?.items?.length ?? 0) === 0) {
      NotificationUtil.warning({
        message: t(
          'admin:data.media.selectFilesToDelete',
          'Vui lòng chọn ít nhất một media để xóa'
        ),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, mediaData?.items?.length, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Lưu số lượng media đã chọn trước khi xóa để hiển thị thông báo
      const deletedCount = selectedRowKeys.length;

      // Gọi API xóa nhiều media cùng lúc
      await deleteMultipleMedia(selectedRowKeys as string[]);

      // Đảm bảo đặt lại selectedRowKeys thành mảng rỗng trước khi đóng modal
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('admin:data.media.bulkDeleteSuccess', `Đã xóa ${deletedCount} media thành công`),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting media:', error);
      NotificationUtil.error({
        message: t('admin:data.media.bulkDeleteError', 'Có lỗi xảy ra khi xóa media'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleMedia, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={dataTable.tableData.handleSearch}
            items={dataTable.menuItems}
            onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
            columns={dataTable.columnVisibility.visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
            additionalIcons={[
              {
                icon: 'trash',
                tooltip: t('common.bulkDelete', 'Xóa nhiều'),
                variant: 'primary',
                onClick: () => {
                  // Kiểm tra lại số lượng mục đã chọn và dữ liệu có tồn tại không
                  if (selectedRowKeys.length > 0 && (mediaData?.items?.length ?? 0) > 0) {
                    handleShowBulkDeleteConfirm();
                  } else {
                    NotificationUtil.info({
                      message: t(
                        'admin:data.media.selectFilesToDelete',
                        'Vui lòng chọn ít nhất một media để xóa'
                      ),
                      duration: 3000,
                    });
                  }
                },
                className: 'text-red-500',
                // Chỉ hiển thị khi có dữ liệu và có mục được chọn
                condition: selectedRowKeys.length > 0 && (mediaData?.items?.length ?? 0) > 0,
              },
            ]}
          />
        </div>

        {/* Hiển thị ActiveFilters */}
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {mediaToView && (
            <MediaDetailView
              media={mediaToView}
              onClose={handleCloseViewForm}
              onDelete={() => handleShowDeleteConfirm(mediaToView)}
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<AdminMediaDto>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={mediaData?.items || []}
            rowKey="id"
            loading={isLoadingMedia}
            sortable={true}
            selectable={true}
            rowSelection={{
              selectedRowKeys,
              onChange: keys => setSelectedRowKeys(keys),
            }}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: mediaData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: mediaData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t('admin:data.media.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa media này?')}
        itemName={fileToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={
          selectedRowKeys.length > 0
            ? t(
                'admin:data.media.confirmBulkDeleteMessage',
                `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} media đã chọn?`
              )
            : t('admin:data.media.noFilesSelected', 'Không có media nào được chọn')
        }
      />
    </div>
  );
};

export default MediaPage;
