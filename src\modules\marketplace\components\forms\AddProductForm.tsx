import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
} from '@/shared/components/common';

import { z } from 'zod';
import { UserProductCategory, CreateUserProductDto } from '../../types/user-product.types';
import { loadUserKnowledgeFiles, loadUserAgents, SourceOption } from '../../services/user-source.service';
import { SUPPORTED_FILE_TYPES, DOCUMENT_ACCEPT_TYPES, SUPPORTED_FORMATS_TEXT } from '../../constants/fileTypes';

// Create a custom schema for user product creation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAddProductSchema = (t: any) => z.object({
  name: z
    .string()
    .min(3, t('marketplace:product.validation.nameMin', 'Tên sản phẩm phải có ít nhất 3 ký tự'))
    .max(500, t('marketplace:product.validation.nameMax', 'Tên sản phẩm không được vượt quá 500 ký tự')),
  description: z.string().min(1, t('marketplace:product.validation.descriptionRequired', 'Mô tả sản phẩm là bắt buộc')),
  listedPrice: z
    .string()
    .min(1, t('marketplace:product.validation.listedPriceRequired', 'Giá niêm yết là bắt buộc'))
    .transform((val) => Number(val))
    .pipe(z.number().min(0, t('marketplace:product.validation.listedPriceMin', 'Giá niêm yết không được âm'))),
  discountedPrice: z
    .string()
    .min(1, t('marketplace:product.validation.discountedPriceRequired', 'Giá khuyến mãi là bắt buộc'))
    .transform((val) => Number(val))
    .pipe(z.number().min(0, t('marketplace:product.validation.discountedPriceMin', 'Giá khuyến mãi không được âm'))),
  category: z.nativeEnum(UserProductCategory, {
    errorMap: () => ({ message: t('marketplace:product.validation.categoryRequired', 'Loại sản phẩm là bắt buộc') }),
  }),
  sourceId: z.string().optional(),
  imagesMediaTypes: z.array(z.string()).optional(),
  userManualMediaType: z.string().optional(),
  detailMediaType: z.string().optional(),
});

// Interface cho response từ API tạo sản phẩm (theo response thực tế)
interface CreateProductResponse {
  code: number;
  message: string;
  result: {
    id: string;
    name: string;
    description: string;
    listedPrice: string;
    discountedPrice: string;
    category: string;
    sourceId: string;
    createdAt: string;
    updatedAt: string;
    seller: {
      name: string;
      avatar: string;
      type: string;
    };
    status: string;
    uploadUrls: {
      productId: string;
      imagesUploadUrls?: Array<{
        url: string;
        key: string;
        index: number;
      }>;
      detailUploadUrl?: {
        url: string;
        key: string;
        expiresAt: number;
      };
      userManualUploadUrl?: {
        url: string;
        key: string;
        expiresAt: number;
      };
    };
  };
}

export interface AddProductFormProps {
  /**
   * Function to handle form submission
   * Should return a Promise with the API response containing upload URLs
   */
  onSubmit: (values: CreateUserProductDto) => Promise<CreateProductResponse>;

  /**
   * Function to handle form cancellation
   */
  onCancel: () => void;

  /**
   * Function to handle success after upload completion
   */
  onSuccess?: () => void;

  /**
   * Initial values for editing (optional)
   */
  initialValues?: Partial<CreateUserProductDto>;
}

/**
 * Form component for adding new products to the marketplace (User version)
 */
const AddProductForm: React.FC<AddProductFormProps> = ({
  onSubmit,
  onCancel,
  onSuccess,
  initialValues,
}) => {
  const { t } = useTranslation(['marketplace', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Direct upload function for image files (theo pattern admin AddProductForm)
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    console.log(`🔍 [uploadImageFile] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl.substring(0, 100) + '...' // Truncate URL for logging
    });

    try {
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      console.log(`🔍 [uploadImageFile] Response status:`, response.status);
      console.log(`🔍 [uploadImageFile] Response ok:`, response.ok);

      if (!response.ok) {
        const responseText = await response.text();
        console.error(`❌ [uploadImageFile] Upload failed:`, {
          status: response.status,
          statusText: response.statusText,
          responseBody: responseText.substring(0, 500) // Truncate response for logging
        });
        throw new Error(`Failed to upload image: ${response.status} ${response.statusText}`);
      }

      console.log(`✅ [uploadImageFile] Upload successful for:`, file.name);
      return response;
    } catch (error) {
      console.error(`❌ [uploadImageFile] Exception during upload:`, error);
      throw error;
    }
  };

  // Direct upload function for document files (theo pattern admin AddProductForm)
  const uploadDocumentFile = async (file: File, presignedUrl: string) => {
    console.log(`🔍 [uploadDocumentFile] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl.substring(0, 100) + '...' // Truncate URL for logging
    });

    try {
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      console.log(`🔍 [uploadDocumentFile] Response status:`, response.status);
      console.log(`🔍 [uploadDocumentFile] Response ok:`, response.ok);

      if (!response.ok) {
        const responseText = await response.text();
        console.error(`❌ [uploadDocumentFile] Upload failed:`, {
          status: response.status,
          statusText: response.statusText,
          responseBody: responseText.substring(0, 500) // Truncate response for logging
        });
        throw new Error(`Failed to upload document: ${response.status} ${response.statusText}`);
      }

      console.log(`✅ [uploadDocumentFile] Upload successful for:`, file.name);
      return response;
    } catch (error) {
      console.error(`❌ [uploadDocumentFile] Exception during upload:`, error);
      throw error;
    }
  };

  // Form ref
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const formRef = React.useRef<any>(null);

  // File upload refs
  const imageInputRef = useRef<HTMLInputElement>(null);
  const userManualInputRef = useRef<HTMLInputElement>(null);
  const detailInputRef = useRef<HTMLInputElement>(null);

  // File states
  const [images, setImages] = useState<File[]>([]);
  const [userManuals, setUserManuals] = useState<File[]>([]);
  const [userManualTypes, setUserManualTypes] = useState<string[]>([]);
  const [detail, setDetail] = useState<File | null>(null);

  // Category and source states
  const [selectedCategory, setSelectedCategory] = useState<UserProductCategory>(
    initialValues?.category || UserProductCategory.KNOWLEDGE_FILE
  );
  const [selectedSourceId, setSelectedSourceId] = useState<string>(initialValues?.sourceId || '');
  const [sourceOptions, setSourceOptions] = useState<SourceOption[]>([]);
  const [loadingSourceOptions, setLoadingSourceOptions] = useState(false);

  // Default values for the form - memoize để tránh re-render
  const defaultValues = React.useMemo(() => ({
    name: initialValues?.name || '',
    description: initialValues?.description || '',
    listedPrice: initialValues?.listedPrice?.toString() || '0',
    discountedPrice: initialValues?.discountedPrice?.toString() || '0',
    category: initialValues?.category || UserProductCategory.KNOWLEDGE_FILE,
    sourceId: initialValues?.sourceId || '',
    imagesMediaTypes: initialValues?.imagesMediaTypes || ['image/jpeg', 'image/png'],
    userManualMediaType: initialValues?.userManualMediaType || SUPPORTED_FILE_TYPES.PDF,
    detailMediaType: initialValues?.detailMediaType || SUPPORTED_FILE_TYPES.PDF,
  }), [initialValues]);

  // Load initial source options when component mounts
  useEffect(() => {
    loadSourceOptions(selectedCategory);
  }, [selectedCategory]);

  // Function to load source options based on category
  const loadSourceOptions = async (category: UserProductCategory) => {
    setLoadingSourceOptions(true);
    try {
      let loadFunction;
      switch (category) {
        case UserProductCategory.KNOWLEDGE_FILE:
          loadFunction = loadUserKnowledgeFiles;
          break;
        case UserProductCategory.AGENT:
          loadFunction = loadUserAgents;
          break;
        default:
          setSourceOptions([]);
          return;
      }

      const result = await loadFunction('', { page: 1, limit: 100 });
      const items = Array.isArray(result) ? result : result.items;

      console.log('Loaded user source options:', items);
      setSourceOptions(items);
    } catch (error) {
      console.error('Error loading user source options:', error);
      setSourceOptions([]);
    } finally {
      setLoadingSourceOptions(false);
    }
  };

  // Handle category change
  const handleCategoryChange = (category: UserProductCategory) => {
    setSelectedCategory(category);
    setSelectedSourceId(''); // Reset source ID when category changes

    // Update form value
    if (formRef.current) {
      formRef.current.setValue('category', category, { shouldValidate: false, shouldDirty: false });
      formRef.current.setValue('sourceId', '', { shouldValidate: false, shouldDirty: false });
    }

    // Load new source options
    loadSourceOptions(category);
  };

  // Handle source selection
  const handleSourceChange = useCallback((value: string | number | string[] | number[]) => {
    // Convert value to string since sourceId should be string
    const stringValue = Array.isArray(value) ? value[0]?.toString() || '' : value.toString();
    setSelectedSourceId(stringValue);

    // Update form value
    if (formRef.current) {
      formRef.current.setValue('sourceId', stringValue, { shouldValidate: false, shouldDirty: false });
    }

    console.log('Selected user source:', { id: stringValue });
  }, []);

  // Handle form submission
  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      setIsSubmitting(true);

      // Get image media types
      const imageTypes = images.map(image => image.type);

      // Convert string values to numbers for price fields
      const productData: CreateUserProductDto = {
        name: values.name as string,
        description: values.description as string,
        category: values.category as UserProductCategory,
        listedPrice: Number(values.listedPrice),
        discountedPrice: Number(values.discountedPrice),
        sourceId: selectedCategory === UserProductCategory.OTHER
          ? undefined
          : (values.sourceId as string || selectedSourceId), // Sử dụng sourceId từ form hoặc state
        imagesMediaTypes: imageTypes.length > 0 ? imageTypes : (values.imagesMediaTypes as string[]),
        userManualMediaType: userManualTypes.length > 0
          ? userManualTypes.join(',')
          : (values.userManualMediaType as string),
        detailMediaType: detail?.type || (values.detailMediaType as string),
      };

      console.log('🔍 [SUBMIT] Submitting user product with data:', productData);
      console.log('🔍 [SUBMIT] Files to upload:', {
        images: images.length,
        userManuals: userManuals.length,
        detail: !!detail
      });
      console.log('🔍 [SUBMIT] Image files:', images.map(img => ({ name: img.name, type: img.type, size: img.size })));
      console.log('🔍 [SUBMIT] User manual files:', userManuals.map(file => ({ name: file.name, type: file.type, size: file.size })));
      console.log('🔍 [SUBMIT] Detail file:', detail ? { name: detail.name, type: detail.type, size: detail.size } : null);

      // Gọi callback onSubmit để tạo sản phẩm và nhận về response với upload URLs
      const createResult = await onSubmit(productData);
      console.log('✅ [SUBMIT] Product created successfully:', createResult);
      console.log('🔍 [SUBMIT] Response structure:', {
        hasResult: !!createResult?.result,
        hasUploadUrls: !!(createResult as { uploadUrls?: unknown })?.uploadUrls,
        resultUploadUrls: !!createResult?.result?.uploadUrls,
        keys: Object.keys(createResult || {})
      });

      // Kiểm tra response structure - có thể uploadUrls ở level root hoặc trong result
      let uploadUrls = null;
      if (createResult?.result?.uploadUrls) {
        uploadUrls = createResult.result.uploadUrls;
        console.log('🔍 [SUBMIT] Found uploadUrls in result.uploadUrls');
      } else {
        // Kiểm tra uploadUrls ở root level
        const rootUploadUrls = (createResult as unknown as { uploadUrls?: typeof createResult.result.uploadUrls })?.uploadUrls;
        if (rootUploadUrls) {
          uploadUrls = rootUploadUrls;
          console.log('🔍 [SUBMIT] Found uploadUrls in root.uploadUrls');
        }
      }

      if (!uploadUrls) {
        console.warn('⚠️ [SUBMIT] No uploadUrls in response, skipping file uploads');
        if (onSuccess) {
          onSuccess();
        }
        return;
      }

      console.log('🔍 [SUBMIT] Upload URLs received:', uploadUrls);

      // Debug: Kiểm tra state của files trước khi upload
      console.log('🔍 [DEBUG] Current file states:');
      console.log('🔍 [DEBUG] - images.length:', images.length);
      console.log('🔍 [DEBUG] - images:', images.map(img => ({ name: img.name, type: img.type, size: img.size })));
      console.log('🔍 [DEBUG] - userManuals.length:', userManuals.length);
      console.log('🔍 [DEBUG] - userManuals:', userManuals.map(file => ({ name: file.name, type: file.type, size: file.size })));
      console.log('🔍 [DEBUG] - detail:', detail ? { name: detail.name, type: detail.type, size: detail.size } : null);

      // Debug: Kiểm tra uploadUrls structure
      console.log('🔍 [DEBUG] Upload URLs structure:');
      console.log('🔍 [DEBUG] - imagesUploadUrls exists:', !!uploadUrls.imagesUploadUrls);
      console.log('🔍 [DEBUG] - imagesUploadUrls length:', uploadUrls.imagesUploadUrls?.length || 0);
      console.log('🔍 [DEBUG] - userManualUploadUrl exists:', !!uploadUrls.userManualUploadUrl);
      console.log('🔍 [DEBUG] - detailUploadUrl exists:', !!uploadUrls.detailUploadUrl);

      // Upload files nếu có
      const allUploadPromises: Promise<void>[] = [];

      // Force check: Nếu có files nhưng không có uploadUrls, log warning
      if (images.length > 0 && !uploadUrls.imagesUploadUrls) {
        console.warn('⚠️ [FORCE_CHECK] Images exist but no imagesUploadUrls provided!');
      }
      if (userManuals.length > 0 && !uploadUrls.userManualUploadUrl) {
        console.warn('⚠️ [FORCE_CHECK] User manuals exist but no userManualUploadUrl provided!');
      }
      if (detail && !uploadUrls.detailUploadUrl) {
        console.warn('⚠️ [FORCE_CHECK] Detail exists but no detailUploadUrl provided!');
      }

      // Upload images nếu có - sử dụng PUT request trực tiếp
      if (images.length > 0 && uploadUrls.imagesUploadUrls) {
        console.log('🔍 [IMAGES] Starting image uploads...');
        console.log('🔍 [IMAGES] Images count:', images.length);
        console.log('🔍 [IMAGES] Upload URLs count:', uploadUrls.imagesUploadUrls.length);
        console.log('🔍 [IMAGES] Upload URLs:', uploadUrls.imagesUploadUrls);

        const imageUploadPromises = images.map(async (file: File, index: number) => {
          const uploadUrlData = uploadUrls?.imagesUploadUrls?.[index];
          console.log(`🔍 [IMAGES] Processing image ${index + 1}:`, {
            file: { name: file.name, type: file.type, size: file.size },
            uploadUrlData: uploadUrlData
          });

          if (!uploadUrlData?.url) {
            console.warn(`⚠️ [IMAGES] No upload URL for image ${index + 1}`);
            return;
          }

          console.log(`🔍 [IMAGES] Uploading image ${index + 1} to:`, uploadUrlData.url);

          try {
            await uploadImageFile(file, uploadUrlData.url);
            console.log(`✅ [IMAGES] Image ${index + 1} uploaded successfully`);
            console.log(`✅ [IMAGES] Image ${index + 1} key:`, uploadUrlData.key);
            console.log(`✅ [IMAGES] Image ${index + 1} will be available at: https://cdn.redai.vn/${uploadUrlData.key}`);
          } catch (error) {
            console.error(`❌ [IMAGES] Exception uploading image ${index + 1}:`, error);
            throw error;
          }
        });
        allUploadPromises.push(...imageUploadPromises);
      } else {
        console.log('❌ [IMAGES] Image upload condition not met:');
        console.log('🔍 [IMAGES] - Images length > 0:', images.length > 0);
        console.log('🔍 [IMAGES] - imagesUploadUrls exists:', !!uploadUrls?.imagesUploadUrls);
        if (uploadUrls?.imagesUploadUrls) {
          console.log('🔍 [IMAGES] - imagesUploadUrls length:', uploadUrls.imagesUploadUrls.length);
        }
      }

      // Upload user manual file nếu có - chỉ upload file đầu tiên vì chỉ có 1 URL
      if (userManuals.length > 0 && uploadUrls.userManualUploadUrl) {
        console.log('🔍 [USER_MANUAL] Starting user manual upload...');
        const file = userManuals[0];
        const uploadUrlData = uploadUrls.userManualUploadUrl;

        if (userManuals.length > 1) {
          console.warn(`⚠️ [USER_MANUAL] Multiple user manual files selected but only uploading the first one: ${file.name}`);
        }

        const userManualUploadPromise = (async () => {
          console.log(`🔍 [USER_MANUAL] Uploading user manual:`, {
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            uploadUrl: uploadUrlData.url
          });

          try {
            await uploadDocumentFile(file, uploadUrlData.url);
            console.log(`✅ [USER_MANUAL] User manual uploaded successfully`);
            console.log(`✅ [USER_MANUAL] User manual key:`, uploadUrlData.key);
            console.log(`✅ [USER_MANUAL] User manual will be available at: https://cdn.redai.vn/${uploadUrlData.key}`);
          } catch (error) {
            console.error(`❌ [USER_MANUAL] Exception uploading user manual:`, error);
            throw error;
          }
        })();
        allUploadPromises.push(userManualUploadPromise);
      } else {
        console.log('❌ [USER_MANUAL] User manual upload condition not met:');
        console.log('🔍 [USER_MANUAL] - User manuals length > 0:', userManuals.length > 0);
        console.log('🔍 [USER_MANUAL] - userManualUploadUrl exists:', !!uploadUrls?.userManualUploadUrl);
      }

      // Upload detail file nếu có - sử dụng PUT request trực tiếp
      if (detail && uploadUrls.detailUploadUrl) {
        console.log('🔍 [DETAIL] Starting detail file upload...');
        const uploadUrlData = uploadUrls.detailUploadUrl;

        const detailUploadPromise = (async () => {
          console.log(`🔍 [DETAIL] Uploading detail file:`, {
            fileName: detail.name,
            fileSize: detail.size,
            fileType: detail.type,
            uploadUrl: uploadUrlData.url
          });

          try {
            await uploadDocumentFile(detail, uploadUrlData.url);
            console.log('✅ [DETAIL] Detail file uploaded successfully');
            console.log(`✅ [DETAIL] Detail file key:`, uploadUrlData.key);
            console.log(`✅ [DETAIL] Detail file will be available at: https://cdn.redai.vn/${uploadUrlData.key}`);
          } catch (error) {
            console.error('❌ [DETAIL] Exception uploading detail file:', error);
            throw error;
          }
        })();
        allUploadPromises.push(detailUploadPromise);
      } else {
        console.log('❌ [DETAIL] Detail upload condition not met:');
        console.log('🔍 [DETAIL] - Detail exists:', !!detail);
        console.log('🔍 [DETAIL] - detailUploadUrl exists:', !!uploadUrls?.detailUploadUrl);
      }

      // Đợi tất cả uploads hoàn thành
      console.log('🔍 [UPLOAD] Total upload promises:', allUploadPromises.length);
      if (allUploadPromises.length > 0) {
        try {
          console.log('🔍 [UPLOAD] Starting parallel uploads...');
          await Promise.all(allUploadPromises);
          console.log('🎉 [UPLOAD] All uploads completed successfully');
        } catch (uploadError) {
          console.error('❌ [UPLOAD] Upload error:', uploadError);
          throw new Error(`Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
        }
      } else {
        console.log('ℹ️ [UPLOAD] No files to upload');
      }

      console.log('✅ [SUBMIT] Product creation and file uploads completed successfully');

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('❌ [SUBMIT] Error submitting user product form:', error);
      throw error; // Re-throw để component cha có thể xử lý error
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image file selection
  const handleImageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    console.log('🔍 [IMAGE_HANDLER] Files selected:', files?.length || 0);

    if (!files || files.length === 0) {
      console.log('🔍 [IMAGE_HANDLER] No files selected, returning');
      return;
    }

    const fileArray = Array.from(files);
    console.log('🔍 [IMAGE_HANDLER] File array:', fileArray.map(f => ({ name: f.name, type: f.type, size: f.size })));

    setImages(prev => {
      const newImages = [...prev, ...fileArray];
      console.log('🔍 [IMAGE_HANDLER] Updated images state:', newImages.map(f => ({ name: f.name, type: f.type, size: f.size })));
      return newImages;
    });

    // Reset input để có thể chọn lại cùng file
    if (e.target) {
      e.target.value = '';
    }
  }, []);

  // Handle user manual file selection
  const handleUserManualChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    console.log('🔍 [USER_MANUAL_HANDLER] Files selected:', files?.length || 0);

    if (!files || files.length === 0) {
      console.log('🔍 [USER_MANUAL_HANDLER] No files selected, clearing state');
      setUserManuals([]);
      setUserManualTypes([]);
      return;
    }

    // Convert FileList to array
    const fileArray = Array.from(files);
    console.log('🔍 [USER_MANUAL_HANDLER] File array:', fileArray.map(f => ({ name: f.name, type: f.type, size: f.size })));

    // Extract file types
    const fileTypes = fileArray.map(file => file.type);

    // Update states
    setUserManuals(fileArray);
    setUserManualTypes(fileTypes);
    console.log('🔍 [USER_MANUAL_HANDLER] Updated userManuals state:', fileArray.map(f => ({ name: f.name, type: f.type, size: f.size })));

    // Reset input để có thể chọn lại cùng file
    if (e.target) {
      e.target.value = '';
    }
  }, []);

  // Handle detail file selection
  const handleDetailChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    console.log('🔍 [DETAIL_HANDLER] Files selected:', files?.length || 0);

    if (!files || files.length === 0) {
      console.log('🔍 [DETAIL_HANDLER] No files selected, clearing state');
      setDetail(null);
      return;
    }

    const selectedFile = files[0];
    console.log('🔍 [DETAIL_HANDLER] Selected file:', { name: selectedFile.name, type: selectedFile.type, size: selectedFile.size });
    setDetail(selectedFile);

    // Reset input để có thể chọn lại cùng file
    if (e.target) {
      e.target.value = '';
    }
  }, []);

  // Remove image
  const handleRemoveImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  // Remove specific user manual by index
  const handleRemoveUserManualByIndex = (index: number) => {
    setUserManuals(prev => {
      const newUserManuals = prev.filter((_, i) => i !== index);

      // Update types as well
      const newTypes = newUserManuals.map(file => file.type);
      setUserManualTypes(newTypes);

      return newUserManuals;
    });
  };

  // Remove detail
  const handleRemoveDetail = () => {
    setDetail(null);
    if (detailInputRef.current) {
      detailInputRef.current.value = '';
    }
  };

  // List of product categories
  const categoryOptions = Object.values(UserProductCategory).map(category => ({
    value: category,
    label: t(`marketplace:product.category.${category}`, category),
  }));

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('marketplace:product.addNew', 'Thêm sản phẩm mới')}
        </Typography>

        <Form
          ref={formRef}
          defaultValues={defaultValues}
          schema={createAddProductSchema(t)}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          className="space-y-6"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('marketplace:product.basicInfo', 'Thông tin cơ bản')}
            </Typography>

            <FormItem
              name="name"
              label={t('marketplace:product.form.name', 'Tên sản phẩm')}
              required
            >
              <Input
                fullWidth
                placeholder={t('marketplace:product.form.namePlaceholder', 'Nhập tên sản phẩm')}
              />
            </FormItem>

            <FormItem
              name="description"
              label={t('marketplace:product.form.description', 'Mô tả')}
              required
            >
              <Textarea
                rows={5}
                placeholder={t('marketplace:product.form.descriptionPlaceholder', 'Nhập mô tả sản phẩm')}
              />
            </FormItem>

            <FormItem
              name="category"
              label={t('marketplace:product.form.category', 'Loại sản phẩm')}
              required
            >
              <Select
                options={categoryOptions}
                value={selectedCategory}
                onChange={(value) => handleCategoryChange(value as UserProductCategory)}
                fullWidth
              />
            </FormItem>

            {selectedCategory !== UserProductCategory.OTHER && (
              <FormItem
                name="sourceId"
                label={t('marketplace:product.form.sourceId', 'Source ID')}
                required
              >
                <Select
                  options={sourceOptions}
                  value={selectedSourceId}
                  onChange={handleSourceChange}
                  placeholder={t('marketplace:product.form.sourceIdPlaceholder', 'Tìm kiếm và chọn source')}
                  searchable
                  loading={loadingSourceOptions}
                  fullWidth
                />
              </FormItem>
            )}
          </div>

          <Divider />

          {/* Price Information */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('marketplace:product.priceInfo', 'Thông tin giá')}
            </Typography>

            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              <FormItem
                name="listedPrice"
                label={t('marketplace:product.form.listedPrice', 'Giá niêm yết')}
                required
              >
                <Input
                  type="number"
                  min={0}
                  fullWidth
                  placeholder="0"
                />
              </FormItem>

              <FormItem
                name="discountedPrice"
                label={t('marketplace:product.form.discountedPrice', 'Giá khuyến mãi')}
                required
              >
                <Input
                  type="number"
                  min={0}
                  fullWidth
                  placeholder="0"
                />
              </FormItem>
            </FormGrid>
          </div>

          <Divider />

          {/* Media Types */}
          <div className="space-y-4">
            <Typography variant="subtitle1" className="font-medium">
              {t('marketplace:product.mediaTypes', 'Tệp tin & Hình ảnh')}
            </Typography>

            {/* Image Upload */}
            <FormItem
              name="images"
              label={t('marketplace:product.form.images', 'Hình ảnh sản phẩm')}
              helpText={t('marketplace:product.form.imagesHelp', 'Hỗ trợ định dạng: JPG, PNG')}
            >
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    ref={imageInputRef}
                    type="file"
                    accept="image/jpeg, image/png"
                    onChange={handleImageChange}
                    multiple
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => imageInputRef.current?.click()}
                    leftIcon={<Icon name="upload" />}
                  >
                    {t('marketplace:product.form.selectImages', 'Chọn hình ảnh')}
                  </Button>
                </div>

                {images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-3">
                    {images.map((image, index) => (
                      <div key={index} className="relative group">
                        <div className="border border-border rounded-md overflow-hidden h-24">
                          <img
                            src={URL.createObjectURL(image)}
                            alt={`Preview ${index}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <Button
                          variant='primary'
                          onClick={() => handleRemoveImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Icon name="x" size="sm" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </FormItem>

            <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
              {/* User Manual Upload */}
              <FormItem
                name="userManual"
                label={t('marketplace:product.form.userManual', 'Hướng dẫn sử dụng')}
                helpText={t('marketplace:product.form.userManualHelp', `Hỗ trợ định dạng: ${SUPPORTED_FORMATS_TEXT}`)}
              >
                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      ref={userManualInputRef}
                      type="file"
                      accept={DOCUMENT_ACCEPT_TYPES}
                      onChange={handleUserManualChange}
                      multiple
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => userManualInputRef.current?.click()}
                      leftIcon={<Icon name="upload" />}
                    >
                      {t('marketplace:product.form.selectFiles', 'Chọn tệp tin')}
                    </Button>
                  </div>

                  {userManuals.length > 0 && (
                    <div className="mt-3">
                      <div className="flex items-center justify-between mb-2">
                        <Typography variant="body2" className="font-medium">
                          {t('marketplace:product.form.selectedFiles', 'Tệp tin đã chọn')}
                        </Typography>
                      </div>
                      <div className="space-y-2">
                        {userManuals.map((file, index) => (
                          <div key={index} className="flex items-center p-2 bg-card-muted rounded border border-border">
                            <Icon name="file-text" className="mr-2 text-primary" />
                            <span className="text-sm truncate flex-1">{file.name}</span>
                            <span className="text-xs text-muted mx-2">
                              ({file.type})
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              onClick={() => handleRemoveUserManualByIndex(index)}
                              className="text-red-500 hover:text-red-700 p-1"
                            >
                              <Icon name="x" size="sm" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </FormItem>

              {/* Detail Document Upload */}
              <FormItem
                name="detail"
                label={t('marketplace:product.form.detail', 'Tài liệu chi tiết')}
                helpText={t('marketplace:product.form.detailHelp', `Hỗ trợ định dạng: ${SUPPORTED_FORMATS_TEXT}`)}
              >
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      ref={detailInputRef}
                      type="file"
                      accept={DOCUMENT_ACCEPT_TYPES}
                      onChange={handleDetailChange}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => detailInputRef.current?.click()}
                      leftIcon={<Icon name="upload" />}
                    >
                      {detail ? t('marketplace:product.form.changeFile', 'Thay đổi file') : t('marketplace:product.form.selectFile', 'Chọn file')}
                    </Button>
                  </div>

                  {detail && (
                    <div className="flex items-center mt-2 p-2 bg-card-muted rounded border border-border">
                      <Icon name="file-text" className="mr-2 text-primary" />
                      <span className="text-sm truncate flex-1">{detail.name}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={handleRemoveDetail}
                        className="ml-2 text-red-500 hover:text-red-700 p-1"
                      >
                        <Icon name="x" size="sm" />
                      </Button>
                    </div>
                  )}
                </div>
              </FormItem>
            </FormGrid>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button variant="outline" onClick={onCancel} type="button" disabled={isSubmitting}>
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button variant="primary" type="submit" isLoading={isSubmitting}>
              {t('common:save', 'Lưu')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default AddProductForm;
