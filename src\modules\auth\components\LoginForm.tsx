import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Controller, FieldValues } from 'react-hook-form';

import { Form, FormItem, Input, Button, Checkbox, PasswordInput } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { createLoginSchema, LoginFormValues } from '../schemas/auth.schema';
import { LoginRequest, User } from '../types/auth.types';

import { VerifyEmailResult, TwoFactorAuthResult } from '../types/auth-response.types';
import { useLogin } from '../hooks/useAuthQuery';
import { useAuthCommon } from '@/shared/hooks';
import { useRecaptcha } from '../hooks/useRecaptcha';
import { useFormErrors } from '@/shared/hooks';
import {
  saveCredentials,
  getCredentials,
  clearCredentials,
  areCredentialsValid,
  isRememberMeChecked,
} from '../utils/auth-storage.utils';

interface LoginFormProps {
  onSuccess?: () => void;
  onForgotPassword?: () => void;
}

/**
 * Login form component
 */
const LoginForm: React.FC<LoginFormProps> = ({ onSuccess, onForgotPassword }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setUserAuth, saveVerifyInfo, saveTwoFactorInfo } = useAuthCommon();
  const { mutate: login, isPending } = useLogin();
  const { formRef, setFormErrors } = useFormErrors<LoginFormValues>();

  // Sử dụng hook useRecaptcha để quản lý reCAPTCHA
  const {
    recaptchaToken,
    error: recaptchaError,
    resetRecaptcha,
  } = useRecaptcha('recaptcha-container', 'LOGIN');

  // Hiển thị lỗi reCAPTCHA nếu có
  useEffect(() => {
    if (recaptchaError) {
      setFormErrors({
        username: t('auth.recaptchaError', 'Lỗi xác thực reCAPTCHA: ') + recaptchaError,
      });
    }
  }, [recaptchaError, t, setFormErrors]);

  // Create login schema with translations
  const loginSchema = createLoginSchema(t);

  // Lưu trữ thông tin đăng nhập đã lấy được
  const [savedUsername, setSavedUsername] = useState<string>('');
  const [savedPassword, setSavedPassword] = useState<string>('');
  const [rememberMeChecked, setRememberMeChecked] = useState<boolean>(false);
  // Theo dõi xem người dùng đã thay đổi giá trị trong form chưa
  const [formEdited, setFormEdited] = useState<boolean>(false);

  // Check for saved credentials on component mount
  useEffect(() => {
    console.log('==========================================');
    console.log('CHECKING FOR SAVED CREDENTIALS...');
    console.log('==========================================');

    try {
      // Kiểm tra localStorage trực tiếp
      const rememberMeValue = localStorage.getItem('auth_remember_me');
      const credentialsValue = localStorage.getItem('auth_credentials');

      console.log('Raw localStorage values:', {
        rememberMeValue,
        hasCredentials: !!credentialsValue,
        credentialsLength: credentialsValue ? credentialsValue.length : 0,
      });

      if (credentialsValue) {
        try {
          const parsedCredentials = JSON.parse(credentialsValue);
          console.log('Parsed credentials from localStorage:', {
            username: parsedCredentials.username,
            hasEncryptedPassword: !!parsedCredentials.encryptedPassword,
            timestamp: parsedCredentials.timestamp,
            date: new Date(parsedCredentials.timestamp).toLocaleString(),
          });
        } catch (parseError) {
          console.error('Error parsing credentials from localStorage:', parseError);
        }
      }

      // Kiểm tra xem người dùng đã chọn "Remember me" chưa và thông tin đăng nhập có hợp lệ không
      const isValid = areCredentialsValid();
      console.log('Checking if credentials are valid:', isValid);

      // Lấy trạng thái Remember me
      const isRememberMe = isRememberMeChecked();
      setRememberMeChecked(isRememberMe);
      console.log('Remember me checked:', isRememberMe);

      if (isValid) {
        const savedCredentials = getCredentials();
        console.log('Retrieved credentials:', {
          hasCredentials: !!savedCredentials,
          username: savedCredentials?.username,
          hasPassword: !!savedCredentials?.password,
          passwordLength: savedCredentials?.password ? savedCredentials.password.length : 0,
        });

        if (savedCredentials && savedCredentials.username) {
          console.log('Found valid saved credentials with Remember me checked');

          // Lưu thông tin đăng nhập để sử dụng trong form
          setSavedUsername(savedCredentials.username);
          setSavedPassword(savedCredentials.password);

          console.log('Saved credentials for form:', {
            username: savedCredentials.username,
            passwordLength: savedCredentials.password.length,
          });
        } else {
          console.log('No valid credentials found despite areCredentialsValid returning true');
          clearCredentials();
        }
      } else {
        // Clear expired credentials or if Remember me is not checked
        console.log(
          'No valid credentials found or Remember me not checked, clearing any saved credentials'
        );
        clearCredentials();
      }
    } catch (error) {
      console.error('Error checking saved credentials:', error);
      clearCredentials();
    }

    console.log('==========================================');
  }, []);

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    // Use type assertion with a specific type instead of 'any'
    const loginValues = values as LoginFormValues;

    // Đánh dấu form đã được chỉnh sửa để tránh quay lại giá trị đã lưu
    setFormEdited(true);

    console.log('==========================================');
    console.log('FORM SUBMITTED');
    console.log('==========================================');
    console.log('Form values:', {
      username: loginValues.username,
      passwordLength: loginValues.password ? loginValues.password.length : 0,
      rememberMe: loginValues.rememberMe,
    });

    // Save credentials if remember me is checked
    if (loginValues.rememberMe) {
      console.log('Remember me is checked, saving credentials for:', loginValues.username);
      if (loginValues.username && loginValues.password) {
        // Lưu thông tin đăng nhập
        saveCredentials(loginValues.username, loginValues.password);

        // Kiểm tra xem đã lưu thành công chưa
        setTimeout(() => {
          const rememberMeValue = localStorage.getItem('auth_remember_me');
          const credentialsValue = localStorage.getItem('auth_credentials');

          console.log('Verification after saving:', {
            rememberMeValue,
            hasCredentials: !!credentialsValue,
          });

          if (credentialsValue) {
            try {
              const parsedCredentials = JSON.parse(credentialsValue);
              console.log('Saved credentials:', {
                username: parsedCredentials.username,
                hasEncryptedPassword: !!parsedCredentials.encryptedPassword,
                timestamp: parsedCredentials.timestamp,
              });
            } catch (parseError) {
              console.error('Error parsing saved credentials:', parseError);
            }
          }
        }, 100);
      } else {
        console.error('Cannot save credentials: username or password is empty');
      }
    } else {
      console.log('Remember me is not checked, clearing credentials');
      clearCredentials();
    }
    console.log('==========================================');

    // Reset form errors
    setFormErrors({});

    // Kiểm tra xem reCAPTCHA đã được xác thực chưa

    // Call login API
    login(
      {
        // Chuyển đổi từ username sang email theo yêu cầu của API
        email: loginValues.username,
        password: loginValues.password,
        recaptchaToken: recaptchaToken || undefined,
      } as LoginRequest,
      {
        onSuccess: (response: ApiResponseDto<unknown>) => {
          // Reset reCAPTCHA sau khi đăng nhập thành công
          resetRecaptcha();

          // Lưu token vào Redux store thông qua hook useAuth
          if (
            response.result &&
            typeof response.result === 'object' &&
            'accessToken' in response.result
          ) {
            const result = response.result as {
              accessToken?: string;
              expiresIn?: number;
              expiresAt?: number;
              user?: unknown;
            };
            if (result.accessToken) {
              // Ưu tiên sử dụng expiresAt từ API
              const expiresAt = result.expiresAt || 0;
              // Tính toán expiresIn từ expiresAt để tương thích với code cũ
              const expiresIn = Math.floor((expiresAt - Date.now()) / 1000);

              console.log('API response data:', {
                hasExpiresAt: !!result.expiresAt,
                expiresAt: result.expiresAt,
                formattedExpiresAt: result.expiresAt ? new Date(result.expiresAt).toLocaleString() : 'N/A',
                calculatedExpiresIn: expiresIn
              });

              // Lưu thông tin đăng nhập vào Redux
              setUserAuth({
                accessToken: result.accessToken,
                expiresIn: expiresIn, // Vẫn cần truyền expiresIn để tương thích với code cũ
                expiresAt: expiresAt, // Sử dụng expiresAt từ API
                user: result.user as User,
              });

              console.log('Token saved to Redux:', {
                token: result.accessToken,
                expiresAt: expiresAt,
                formattedExpiresAt: new Date(expiresAt).toLocaleString()
              });
            }
          }

          // Kiểm tra mã phản hồi từ backend
          if (response.code === 202) {
            // Yêu cầu xác thực email hoặc số điện thoại
            const result = response.result as VerifyEmailResult;

            // Lưu thông tin vào Redux thông qua hook useAuth
            saveVerifyInfo({
              verifyToken: result.verifyToken,
              expiresIn: result.expiresIn,
              info: result.info,
            });

            // Chuyển hướng đến trang xác thực email
            navigate('/auth/verify-email');
          } else if (response.code === 203) {
            // Yêu cầu xác thực hai lớp
            const result = response.result as TwoFactorAuthResult;

            // Lưu thông tin vào Redux thông qua hook useAuth
            saveTwoFactorInfo({
              verifyToken: result.verifyToken,
              expiresAt: result.expiresAt,
              enabledMethods: result.enabledMethods,
            });

            // Chuyển hướng đến trang xác thực hai lớp
            navigate('/auth/two-factor');
          } else {
            console.log('Any existing token removed when user logged in successfully');


            // Call success callback if provided
            if (onSuccess) {
              onSuccess();
            }

            // Navigate to home page
            navigate('/');
          }
        },
        onError: (error: unknown) => {
          console.error('Login error:', error);

          // Lấy thông báo lỗi từ response API
          let errorMsg = t('auth.loginError', 'Đăng nhập thất bại');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as {
              response: { data?: { message?: string; errors?: Record<string, string> } };
            };

            // Nếu có lỗi cụ thể cho từng field
            if (axiosError.response.data?.errors) {
              // Đặt lỗi cho các field tương ứng
              setFormErrors(axiosError.response.data.errors);
            } else if (axiosError.response.data?.message) {
              // Nếu chỉ có thông báo lỗi chung
              errorMsg = axiosError.response.data.message;
              // Đặt lỗi chung cho field username (hoặc field phù hợp)
              setFormErrors({ username: errorMsg });
            }
          } else {
            // Nếu không có lỗi cụ thể, đặt lỗi chung cho field username
            setFormErrors({ username: errorMsg });
          }

          // Reset reCAPTCHA sau khi đăng nhập thất bại
          resetRecaptcha();

          // Giữ trạng thái form đã chỉnh sửa để không quay lại giá trị đã lưu
          setFormEdited(true);
        },
      }
    );
  };

  // Lưu trữ giá trị hiện tại của form
  const [currentUsername, setCurrentUsername] = useState<string>(savedUsername);
  const [currentPassword, setCurrentPassword] = useState<string>(savedPassword);
  // Thêm state để lưu trữ giá trị hiện tại của checkbox "ghi nhớ đăng nhập"
  const [currentRememberMe, setCurrentRememberMe] = useState<boolean>(rememberMeChecked);

  // Theo dõi xem reCAPTCHA đã được xác thực chưa
  useEffect(() => {
    // Khi recaptchaToken thay đổi (đã xác thực), đánh dấu form đã được chỉnh sửa
    // nhưng không làm mất trạng thái của các trường đã nhập
    if (recaptchaToken) {
      setFormEdited(true);
    }
  }, [recaptchaToken]);

  // Khi savedUsername, savedPassword hoặc rememberMeChecked thay đổi và form chưa được chỉnh sửa
  useEffect(() => {
    if (!formEdited) {
      setCurrentUsername(savedUsername);
      setCurrentPassword(savedPassword);
      setCurrentRememberMe(rememberMeChecked);
    }
  }, [savedUsername, savedPassword, rememberMeChecked, formEdited]);

  return (
    <Form
      ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
      schema={loginSchema}
      onSubmit={handleSubmit}
      className="space-y-4"
      autoComplete="off"
      defaultValues={{
        username: currentUsername,
        password: currentPassword,
        rememberMe: currentRememberMe, // Sử dụng trạng thái Remember me hiện tại
      }}
    >
      <FormItem name="username" label={t('auth.email')} required>
        <Controller
          name="username"
          render={({ field }) => (
            <Input
              type="email"
              fullWidth
              autoComplete="off"
              value={field.value}
              onChange={(e) => {
                // Cập nhật giá trị trong react-hook-form
                field.onChange(e);
                // Cập nhật giá trị hiện tại
                setCurrentUsername(e.target.value);
                // Đánh dấu form đã được chỉnh sửa
                if (!formEdited) setFormEdited(true);
              }}
            />
          )}
        />
      </FormItem>

      <FormItem name="password" label={t('auth.password')} required>
        <Controller
          name="password"
          render={({ field }) => (
            <PasswordInput
              fullWidth
              autoComplete="new-password" // Trick để ngăn trình duyệt tự động điền
              value={field.value}
              onChange={(e) => {
                // Cập nhật giá trị trong react-hook-form
                field.onChange(e);
                // Cập nhật giá trị hiện tại
                setCurrentPassword(e.target.value);
                // Đánh dấu form đã được chỉnh sửa
                if (!formEdited) setFormEdited(true);
              }}
            />
          )}
        />
      </FormItem>

      <div className="flex justify-between items-start mb-4">
        <Controller
          name="rememberMe"
          render={({ field }) => (
            <div className="flex items-center">
              <Checkbox
                checked={field.value}
                onChange={(checked) => {
                  // Cập nhật giá trị trong react-hook-form
                  field.onChange(checked);
                  // Cập nhật state để lưu trữ giá trị hiện tại
                  setCurrentRememberMe(checked);
                  // Đánh dấu form đã được chỉnh sửa
                  if (!formEdited) setFormEdited(true);
                }}
                label={t('auth.rememberMe')}
                variant="filled"
                color="primary"
                size="md"
              />
            </div>
          )}
        />

        <a
          href="#"
          onClick={e => {
            e.preventDefault();
            onForgotPassword?.();
          }}
          className="text-primary hover:text-primary/80 text-sm font-medium"
        >
          {t('auth.forgotPassword')}
        </a>
      </div>

      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300"></div>
        </div>

        {/* Container cho reCAPTCHA */}
        <div
          id="recaptcha-container"
          className="flex justify-center min-h-[78px] dark:border-gray-700 rounded-md"
        ></div>
      </div>

      <Button type="submit" variant="primary" fullWidth isLoading={isPending}>
        {t('auth.signIn')}
      </Button>
    </Form>
  );
};

export default LoginForm;
