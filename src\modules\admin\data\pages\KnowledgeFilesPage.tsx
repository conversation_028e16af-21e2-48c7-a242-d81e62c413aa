import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  ConfirmDeleteModal,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useKnowledgeFiles,
  useCreateKnowledgeFiles,
  useDeleteKnowledgeFile,
} from '@/modules/admin/data/knowledge-files/hooks';
import {
  KnowledgeFileDto,
  KnowledgeFileQueryParams,
  CreateKnowledgeFileDto,
} from '@/modules/admin/data/knowledge-files/types';
import { formatDate } from '@/shared/utils/format';
import { KnowledgeFileCreateForm } from '@/modules/admin/data/components/forms';

// Hàm formatBytes tạm thời
const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Trang quản lý file tri thức
 */
const KnowledgeFilesPage: React.FC = () => {
  const { t } = useTranslation(['data']);
  const [files, setFiles] = useState<KnowledgeFileDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [totalItems, setTotalItems] = useState(0);
  const [fileToDelete, setFileToDelete] = useState<KnowledgeFileDto | null>(null);

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // State cho chọn nhiều file để xóa
  const [selectedFileIds, setSelectedFileIds] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((file: KnowledgeFileDto) => {
    setFileToDelete(file);
    setShowDeleteConfirm(true);
  }, []);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<KnowledgeFileDto>[]>(() => {
    const allColumns: TableColumn<KnowledgeFileDto>[] = [
      {
        key: 'name',
        title: t('data:knowledgeFiles.fileName', 'File name'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'extension',
        title: t('data:knowledgeFiles.fileType', 'Type'),
        dataIndex: 'extension',
        width: '10%',
        sortable: true,
        render: (value: unknown) => (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {value ? String(value) : '-'}
          </span>
        ),
      },
      {
        key: 'storage',
        title: t('data:knowledgeFiles.fileSize', 'Size'),
        dataIndex: 'storage',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatBytes(value as number)}</span>,
      },
      {
        key: 'vectorStoreName',
        title: t('data:knowledgeFiles.vectorStore', 'Vector Store'),
        dataIndex: 'vectorStoreName',
        width: '20%',
        render: (value: unknown) => <span>{value ? String(value) : '-'}</span>,
      },
      {
        key: 'createdAt',
        title: t('data:knowledgeFiles.createdAt', 'Created at'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: KnowledgeFileDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'delete',
              label: t('data:common.delete', 'Delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<KnowledgeFileDto, KnowledgeFileQueryParams>({
    columns,
    filterOptions: [],
    showDateFilter: false,
    createQueryParams: params => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection as 'ASC' | 'DESC' | undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<KnowledgeFileQueryParams>(() => {
    const params: KnowledgeFileQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
      sortBy: dataTable.tableData.sortBy || undefined,
      sortDirection: dataTable.tableData.sortDirection as 'ASC' | 'DESC' | undefined,
    };

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.tableData.sortBy,
    dataTable.tableData.sortDirection,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Hooks để gọi API
  const {
    data: filesData,
    isLoading: isLoadingFiles,
    error: filesError,
  } = useKnowledgeFiles(queryParams);

  const { mutateAsync: createFiles } = useCreateKnowledgeFiles();
  const { deleteFiles } = useDeleteKnowledgeFile();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (filesData) {
      setFiles(filesData.items);
      setTotalItems(filesData.meta.totalItems);
    }

    setIsLoading(isLoadingFiles);
  }, [filesData, filesError, isLoadingFiles]);

  // Xử lý thay đổi trang - sử dụng dataTable
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm - sử dụng dataTable
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp - sử dụng dataTable
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      await deleteFiles(fileToDelete.id);
      setShowDeleteConfirm(false);
      setFileToDelete(null);

      NotificationUtil.success({
        message: t('admin.data.knowledgeFiles.deleteSuccess', 'File deleted successfully'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      NotificationUtil.error({
        message: t('admin.data.knowledgeFiles.deleteError', 'Error deleting file'),
        duration: 3000,
      });
    }
  }, [fileToDelete, deleteFiles, t]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedFileIds.length === 0) {
      NotificationUtil.info({
        message: t(
          'admin.data.knowledgeFiles.selectFilesToDelete',
          'Please select at least one file to delete'
        ),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedFileIds, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedFileIds.length === 0) return;

    try {
      // Xóa tất cả các file đã chọn cùng một lúc
      await deleteFiles(selectedFileIds as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedFileIds([]);

      NotificationUtil.success({
        message: t(
          'admin.data.knowledgeFiles.bulkDeleteSuccess',
          'Selected files deleted successfully'
        ),
        duration: 3000,
      });
    } catch (error) {
      console.error('[KnowledgeFilesPage] Error deleting files:', error);
      NotificationUtil.error({
        message: t('admin.data.knowledgeFiles.bulkDeleteError', 'Error deleting selected files'),
        duration: 3000,
      });
    }
  }, [selectedFileIds, deleteFiles, t]);

  // Xử lý thay đổi lựa chọn
  const handleSelectionChange = useCallback((selectedRowKeys: React.Key[]) => {
    setSelectedFileIds(selectedRowKeys);
  }, []);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý submit form tạo file
  const handleSubmitCreateFile = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsUploading(true);

        // Chuẩn bị dữ liệu cho API
        const fileData = values.files as CreateKnowledgeFileDto[];

        // Đảm bảo fileData là một mảng
        if (!Array.isArray(fileData)) {
          console.error('[KnowledgeFilesPage] File data is not an array:', fileData);
          throw new Error('File data must be an array');
        }

        // Gọi API tạo file - đảm bảo gửi đúng định dạng { files: [...] }
        await createFiles({ files: fileData });
        hideForm();

        NotificationUtil.success({
          message: t('admin.data.knowledgeFiles.uploadSuccess', 'Files uploaded successfully'),
          duration: 3000,
        });
      } catch (error) {
        console.error('[KnowledgeFilesPage] Error creating files:', error);
        NotificationUtil.error({
          message: t('admin.data.knowledgeFiles.uploadError', 'Error uploading files'),
          duration: 3000,
        });
      } finally {
        setIsUploading(false);
      }
    },
    [createFiles, hideForm, t]
  );

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<KnowledgeFileDto>[]>(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: t('data:common.all', 'All'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* Thêm MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showForm()}
            items={[
              {
                id: 'all',
                label: t('data:common.all', 'All'),
                icon: 'list',
                onClick: () => '',
              },
              {
                id: 'bulkDelete',
                label: t('data:common.bulkDelete', 'Batch Delete'),
                icon: 'trash',
                onClick: handleShowBulkDeleteConfirm,
                disabled: selectedFileIds.length === 0,
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* Hiển thị ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form thêm mới */}
        <SlideInForm isVisible={isVisible}>
          <KnowledgeFileCreateForm
            onSubmit={handleSubmitCreateFile}
            onCancel={hideForm}
            isLoading={isUploading}
          />
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<KnowledgeFileDto>
            columns={filteredColumns}
            data={files}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            defaultSort={{
              column: dataTable.tableData.sortBy || '',
              order: dataTable.tableData.sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
            rowSelection={{
              selectedRowKeys: selectedFileIds,
              onChange: handleSelectionChange,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin.data.common.confirmDelete', 'Confirm Delete')}
        message={t(
          'admin.data.knowledgeFiles.confirmDeleteMessage',
          'Are you sure you want to delete this knowledge file?'
        )}
        itemName={fileToDelete?.name}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('admin.data.common.confirmBatchDelete', 'Confirm Batch Delete')}
        message={t(
          'admin.data.knowledgeFiles.confirmBatchDeleteMessage',
          'Are you sure you want to delete {{count}} selected knowledge files?',
          { count: selectedFileIds.length }
        )}
      />
    </div>
  );
};

export default KnowledgeFilesPage;
