import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button } from '@/shared/components/common';
import { HardDrive, TrendingUp } from 'lucide-react';

interface StorageUsageCardProps {
  used: number; // GB used
  total: number; // GB total
  usedFormatted: string; // "2.4 GB"
  totalFormatted: string; // "10 GB"
  remainingFormatted: string; // "7.6 GB"
  percentage: number; // percentage used
  onUpgrade: () => void;
  className?: string;
}

const StorageUsageCard: React.FC<StorageUsageCardProps> = ({
  used,
  total,
  usedFormatted,
  totalFormatted,
  remainingFormatted,
  percentage,
  onUpgrade,
  className = '',
}) => {
  const { t } = useTranslation(['data', 'common']);

  // <PERSON>ác định màu sắc dựa trên phần trăm sử dụng
  const getUsageColor = () => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 75) return 'text-orange-500';
    if (percentage >= 50) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getProgressColor = () => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <HardDrive className="h-5 w-5 text-primary" />
            <Typography variant="h6" className="font-medium">
              {t('data:storage.currentUsage', 'Dung lượng hiện tại')}
            </Typography>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onUpgrade}
            className="flex items-center space-x-1"
          >
            <TrendingUp className="h-4 w-4" />
            <span>{t('data:storage.upgrade', 'Nâng cấp')}</span>
          </Button>
        </div>



        {/* Visual Progress */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Typography variant="body2" className="font-medium">
              {t('data:storage.usageProgress', 'Tiến độ sử dụng')}
            </Typography>
            <Typography variant="body2" className={`font-semibold ${getUsageColor()}`}>
              {percentage.toFixed(1)}%
            </Typography>
          </div>
          
          {/* Custom Progress Bar */}
          <div className="relative">
            <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full ${getProgressColor()} transition-all duration-500 ease-out rounded-full relative`}
                style={{ width: `${Math.min(percentage, 100)}%` }}
              >
                {/* Shine effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
              </div>
            </div>
            
            {/* Usage indicator */}
            <div 
              className="absolute top-0 h-3 w-1 bg-white border border-gray-400 rounded-sm transform -translate-x-1/2"
              style={{ left: `${Math.min(percentage, 100)}%` }}
            />
          </div>
        </div>

        {/* Usage Warning */}
        {percentage >= 80 && (
          <div className={`p-3 rounded-lg ${
            percentage >= 90 
              ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800' 
              : 'bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800'
          }`}>
            <Typography variant="body2" className={`${
              percentage >= 90 ? 'text-red-700 dark:text-red-300' : 'text-orange-700 dark:text-orange-300'
            }`}>
              {percentage >= 90 
                ? t('data:storage.warningCritical', 'Dung lượng sắp hết! Hãy nâng cấp ngay để tránh gián đoạn dịch vụ.')
                : t('data:storage.warningHigh', 'Dung lượng đang cao. Cân nhắc nâng cấp để đảm bảo hiệu suất tốt nhất.')
              }
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default StorageUsageCard;
