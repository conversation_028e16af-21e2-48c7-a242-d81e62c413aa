import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Slider } from '@/shared/components/common';
import { ShoppingCart, HardDrive, X } from 'lucide-react';

interface StorageSliderProps {
  currentStorage: number; // GB hiện tại
  onPurchase: (storageAmount: number, price: number) => void;
  onClose?: () => void; // Hàm đóng slider
  className?: string;
}

const StorageSlider: React.FC<StorageSliderProps> = ({
  currentStorage = 10,
  onPurchase,
  onClose,
  className = '',
}) => {
  const { t } = useTranslation(['data', 'common']);
  
  // Storage options từ 10GB đến 1TB (1000GB)
  const minStorage = 10;
  const maxStorage = 1000;
  const [selectedStorage, setSelectedStorage] = useState(currentStorage);

  // Tính giá dựa trên dung lượng (VND per GB)
  const calculatePrice = useCallback((storage: number) => {
    const basePrice = 10000; // 10,000 VND per GB
    let pricePerGB = basePrice;
    
    // Giảm giá theo tier
    if (storage >= 100) {
      pricePerGB = 8000; // Giảm 20% cho >= 100GB
    } else if (storage >= 50) {
      pricePerGB = 9000; // Giảm 10% cho >= 50GB
    }
    
    return storage * pricePerGB;
  }, []);

  // Format giá tiền
  const formatPrice = useCallback((price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(price);
  }, []);

  // Tính toán thông tin hiển thị
  const storageInfo = useMemo(() => {
    const price = calculatePrice(selectedStorage);
    const monthlyPrice = formatPrice(price);
    const pricePerGB = Math.round(price / selectedStorage);
    const savings = selectedStorage >= 50 ? Math.round((10000 - pricePerGB) / 10000 * 100) : 0;
    
    return {
      storage: selectedStorage,
      price,
      monthlyPrice,
      pricePerGB: formatPrice(pricePerGB),
      savings,
    };
  }, [selectedStorage, calculatePrice, formatPrice]);

  // Xử lý thay đổi slider
  const handleStorageChange = useCallback((value: number) => {
    setSelectedStorage(value);
  }, []);

  // Xử lý mua
  const handlePurchase = useCallback(() => {
    onPurchase(selectedStorage, storageInfo.price);
  }, [selectedStorage, storageInfo.price, onPurchase]);

  // Các mốc storage phổ biến
  const popularSizes = [10, 25, 50, 100, 250, 500, 1000];

  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <HardDrive className="h-6 w-6 text-primary mr-2" />
            <Typography variant="h5" className="font-semibold">
              {t('data:storage.customizeStorage', 'Tùy Chỉnh Dung Lượng')}
            </Typography>
          </div>
          {onClose && (
            <Button
              variant="ghost"
              size="md"
              onClick={onClose}
              className="p-3 h-10 w-10 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              <X className="h-5 w-5" />
            </Button>
          )}
        </div>

        {/* Storage Display */}
        <div className="text-center py-4">
          <Typography variant="h2" className="font-bold text-primary mb-2">
            {selectedStorage} GB
          </Typography>
          <Typography variant="h4" className="font-semibold mb-1">
            {storageInfo.monthlyPrice}
            <span className="text-lg font-normal text-muted-foreground">/tháng</span>
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {storageInfo.pricePerGB} per GB
            {storageInfo.savings > 0 && (
              <span className="ml-2 text-green-600 font-medium">
                (Tiết kiệm {storageInfo.savings}%)
              </span>
            )}
          </Typography>
        </div>

        {/* Slider */}
        <div className="space-y-4">
          <Slider
            value={selectedStorage}
            min={minStorage}
            max={maxStorage}
            step={5}
            onValueChange={handleStorageChange}
            className="w-full"
          />
          
          {/* Storage markers */}
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{minStorage}GB</span>
            <span>100GB</span>
            <span>500GB</span>
            <span>{maxStorage}GB</span>
          </div>
        </div>

        {/* Quick Select Buttons */}
        <div className="space-y-3">
          <Typography variant="body2" className="font-medium text-center">
            {t('data:storage.quickSelect', 'Chọn nhanh:')}
          </Typography>
          <div className="flex flex-wrap gap-2 justify-center">
            {popularSizes.map((size) => (
              <Button
                key={size}
                variant={selectedStorage === size ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setSelectedStorage(size)}
                className="min-w-[60px]"
              >
                {size}GB
              </Button>
            ))}
          </div>
        </div>



        {/* Purchase Button */}
        <Button
          onClick={handlePurchase}
          className="w-full"
          size="lg"
          disabled={selectedStorage <= currentStorage}
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          {selectedStorage <= currentStorage 
            ? t('data:storage.currentPlan', 'Gói hiện tại')
            : t('data:storage.upgrade', 'Nâng cấp ngay')
          }
        </Button>

        {selectedStorage <= currentStorage && (
          <Typography variant="caption" className="text-center text-muted-foreground block">
            {t('data:storage.selectHigher', 'Vui lòng chọn dung lượng cao hơn gói hiện tại')}
          </Typography>
        )}
      </div>
    </Card>
  );
};

export default StorageSlider;
