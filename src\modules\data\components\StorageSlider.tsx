import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Slider } from '@/shared/components/common';
import { ShoppingCart, HardDrive, Check } from 'lucide-react';

interface StorageSliderProps {
  currentStorage: number; // GB hiện tại
  onPurchase: (storageAmount: number, price: number) => void;
  className?: string;
}

const StorageSlider: React.FC<StorageSliderProps> = ({
  currentStorage = 10,
  onPurchase,
  className = '',
}) => {
  const { t } = useTranslation(['data', 'common']);
  
  // Storage options từ 10GB đến 1TB (1000GB)
  const minStorage = 10;
  const maxStorage = 1000;
  const [selectedStorage, setSelectedStorage] = useState(currentStorage);

  // Tính giá dựa trên dung lượng (VND per GB)
  const calculatePrice = useCallback((storage: number) => {
    const basePrice = 10000; // 10,000 VND per GB
    let pricePerGB = basePrice;
    
    // Giảm giá theo tier
    if (storage >= 100) {
      pricePerGB = 8000; // Giảm 20% cho >= 100GB
    } else if (storage >= 50) {
      pricePerGB = 9000; // Giảm 10% cho >= 50GB
    }
    
    return storage * pricePerGB;
  }, []);

  // Format giá tiền
  const formatPrice = useCallback((price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(price);
  }, []);

  // Tính toán thông tin hiển thị
  const storageInfo = useMemo(() => {
    const price = calculatePrice(selectedStorage);
    const monthlyPrice = formatPrice(price);
    const pricePerGB = Math.round(price / selectedStorage);
    const savings = selectedStorage >= 50 ? Math.round((10000 - pricePerGB) / 10000 * 100) : 0;
    
    return {
      storage: selectedStorage,
      price,
      monthlyPrice,
      pricePerGB: formatPrice(pricePerGB),
      savings,
    };
  }, [selectedStorage, calculatePrice, formatPrice]);

  // Xử lý thay đổi slider
  const handleStorageChange = useCallback((value: number) => {
    setSelectedStorage(value);
  }, []);

  // Xử lý mua
  const handlePurchase = useCallback(() => {
    onPurchase(selectedStorage, storageInfo.price);
  }, [selectedStorage, storageInfo.price, onPurchase]);

  // Các mốc storage phổ biến
  const popularSizes = [10, 25, 50, 100, 250, 500, 1000];

  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-2">
            <HardDrive className="h-6 w-6 text-primary mr-2" />
            <Typography variant="h5" className="font-semibold">
              {t('data:storage.customizeStorage', 'Tùy Chỉnh Dung Lượng')}
            </Typography>
          </div>
          <Typography variant="body2" className="text-muted-foreground">
            {t('data:storage.sliderDescription', 'Kéo thanh trượt để chọn dung lượng phù hợp với nhu cầu của bạn')}
          </Typography>
        </div>

        {/* Storage Display */}
        <div className="text-center py-4">
          <Typography variant="h2" className="font-bold text-primary mb-2">
            {selectedStorage} GB
          </Typography>
          <Typography variant="h4" className="font-semibold mb-1">
            {storageInfo.monthlyPrice}
            <span className="text-lg font-normal text-muted-foreground">/tháng</span>
          </Typography>
          <Typography variant="body2" className="text-muted-foreground">
            {storageInfo.pricePerGB} per GB
            {storageInfo.savings > 0 && (
              <span className="ml-2 text-green-600 font-medium">
                (Tiết kiệm {storageInfo.savings}%)
              </span>
            )}
          </Typography>
        </div>

        {/* Slider */}
        <div className="space-y-4">
          <Slider
            value={selectedStorage}
            min={minStorage}
            max={maxStorage}
            step={5}
            onValueChange={handleStorageChange}
            className="w-full"
          />
          
          {/* Storage markers */}
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{minStorage}GB</span>
            <span>100GB</span>
            <span>500GB</span>
            <span>{maxStorage}GB</span>
          </div>
        </div>

        {/* Quick Select Buttons */}
        <div className="space-y-3">
          <Typography variant="body2" className="font-medium text-center">
            {t('data:storage.quickSelect', 'Chọn nhanh:')}
          </Typography>
          <div className="flex flex-wrap gap-2 justify-center">
            {popularSizes.map((size) => (
              <Button
                key={size}
                variant={selectedStorage === size ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setSelectedStorage(size)}
                className="min-w-[60px]"
              >
                {size}GB
              </Button>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="space-y-2">
          <Typography variant="body2" className="font-medium">
            {t('data:storage.included', 'Bao gồm:')}
          </Typography>
          <div className="space-y-1">
            {[
              t('data:storage.feature.unlimited', 'Upload không giới hạn'),
              t('data:storage.feature.backup', 'Backup tự động'),
              t('data:storage.feature.support', 'Hỗ trợ 24/7'),
              selectedStorage >= 50 && t('data:storage.feature.api', 'API access'),
              selectedStorage >= 100 && t('data:storage.feature.priority', 'Hỗ trợ ưu tiên'),
            ].filter(Boolean).map((feature, index) => (
              <div key={index} className="flex items-center text-sm">
                <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Purchase Button */}
        <Button
          onClick={handlePurchase}
          className="w-full"
          size="lg"
          disabled={selectedStorage <= currentStorage}
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          {selectedStorage <= currentStorage 
            ? t('data:storage.currentPlan', 'Gói hiện tại')
            : t('data:storage.upgrade', 'Nâng cấp ngay')
          }
        </Button>

        {selectedStorage <= currentStorage && (
          <Typography variant="caption" className="text-center text-muted-foreground block">
            {t('data:storage.selectHigher', 'Vui lòng chọn dung lượng cao hơn gói hiện tại')}
          </Typography>
        )}
      </div>
    </Card>
  );
};

export default StorageSlider;
