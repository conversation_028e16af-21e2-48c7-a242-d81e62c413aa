import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography } from '@/shared/components/common';
import { AgentMessage } from '../../types';

interface MessageLogTableProps {
  messages?: AgentMessage[];
  isLoading?: boolean;
  className?: string;
}

const MessageLogTable: React.FC<MessageLogTableProps> = ({
  className,
}) => {
  const { t } = useTranslation(['external-agents']);
  
  // TODO: Implement table functionality when needed
  // const columns = [
  //   {
  //     title: t('external-agents:messages.timestamp'),
  //     dataIndex: 'timestamp',
  //     key: 'timestamp',
  //   },
  //   {
  //     title: t('external-agents:messages.type'),
  //     dataIndex: 'type',
  //     key: 'type',
  //   },
  //   {
  //     title: t('external-agents:messages.status'),
  //     dataIndex: 'status',
  //     key: 'status',
  //   },
  //   {
  //     title: t('external-agents:messages.responseTime'),
  //     dataIndex: 'responseTime',
  //     key: 'responseTime',
  //   },
  // ];

  return (
    <Card className={className}>
      <div className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('external-agents:messages.log')}
        </Typography>
        <div className="text-center py-8">
          <Typography variant="body2" className="text-muted-foreground">
            Message Log Table Component
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            This component will be implemented in a future task.
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default MessageLogTable;
