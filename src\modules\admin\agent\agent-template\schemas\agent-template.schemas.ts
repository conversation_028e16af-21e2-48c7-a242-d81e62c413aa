import { z } from 'zod';
import { TFunction } from 'i18next';
import { AgentTemplateStatusEnum, AgentTemplateSortBy, SortDirection } from '../types/agent-template.types';

/**
 * Schema factory cho agent template với hỗ trợ i18n
 */
export const createAgentTemplateSchemas = (t: TFunction) => {
  /**
   * Schema cho tạo agent template
   */
  const createAgentTemplateSchema = z.object({
    name: z.string()
      .min(1, t('admin:agent.template.validation.nameRequired', 'Tên template là bắt buộc'))
      .max(255, t('admin:agent.template.validation.nameMaxLength', 'Tên template không được quá 255 ký tự')),
    typeAgentId: z.string().min(1, t('admin:agent.template.validation.typeRequired', 'Loại agent là bắt buộc')),
    avatarMimeType: z.string().optional(),
    description: z.string().optional(),
    instruction: z.string().optional(),
    status: z.nativeEnum(AgentTemplateStatusEnum).optional().default(AgentTemplateStatusEnum.DRAFT),
  });

  return {
    createAgentTemplateSchema,
  };
};

// Export legacy schema for backward compatibility (deprecated)
export const createAgentTemplateSchema = z.object({
  name: z.string().min(1, 'Tên template là bắt buộc').max(255, 'Tên template không được quá 255 ký tự'),
  typeAgentId: z.string().min(1, 'Loại agent là bắt buộc'),
  avatarMimeType: z.string().optional(),
  description: z.string().optional(),
  instruction: z.string().optional(),
  status: z.nativeEnum(AgentTemplateStatusEnum).optional().default(AgentTemplateStatusEnum.DRAFT),
});

/**
 * Schema cho cập nhật agent template
 */
export const updateAgentTemplateSchema = z.object({
  name: z.string().min(1, 'Tên template là bắt buộc').max(255, 'Tên template không được quá 255 ký tự').optional(),
  typeAgentId: z.string().min(1, 'Loại agent là bắt buộc').optional(),
  avatarMimeType: z.string().optional(),
  description: z.string().optional(),
  instruction: z.string().optional(),
  status: z.nativeEnum(AgentTemplateStatusEnum).optional(),
});

/**
 * Schema cho cập nhật trạng thái agent template
 */
export const updateAgentTemplateStatusSchema = z.object({
  status: z.nativeEnum(AgentTemplateStatusEnum),
});

/**
 * Schema cho query agent template
 */
export const agentTemplateQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(AgentTemplateStatusEnum).optional(),
  sortBy: z.nativeEnum(AgentTemplateSortBy).optional().default(AgentTemplateSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

/**
 * Schema cho khôi phục agent template
 */
export const restoreAgentTemplateSchema = z.object({
  ids: z.array(z.string().min(1)).min(1, 'Phải chọn ít nhất một template để khôi phục'),
});

// Export types từ schemas
export type CreateAgentTemplateFormData = z.infer<typeof createAgentTemplateSchema>;
export type UpdateAgentTemplateFormData = z.infer<typeof updateAgentTemplateSchema>;
export type UpdateAgentTemplateStatusFormData = z.infer<typeof updateAgentTemplateStatusSchema>;
export type AgentTemplateQueryFormData = z.infer<typeof agentTemplateQuerySchema>;
export type RestoreAgentTemplateFormData = z.infer<typeof restoreAgentTemplateSchema>;
