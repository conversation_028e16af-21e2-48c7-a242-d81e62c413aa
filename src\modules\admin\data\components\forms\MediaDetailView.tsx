import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typo<PERSON>, <PERSON>ton, Chip, ConfirmDeleteModal } from '@/shared/components/common';
import { AdminMediaDto } from '@/modules/admin/data/media/types/media.types';

interface MediaDetailViewProps {
  media: AdminMediaDto;
  onClose: () => void;
  onDelete: (mediaId: string) => void;
}

/**
 * Component hiển thị chi tiết media
 */
const MediaDetailView: React.FC<MediaDetailViewProps> = ({ media, onClose, onDelete }) => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho popup xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(() => {
    onDelete(media.id);
    setShowDeleteConfirm(false);
    onClose();
  }, [media.id, onDelete, onClose]);

  return (
    <>
      <div className="p-6">
        <Typography variant="h5">{t('admin:data.media.viewMedia', 'Chi tiết media')}</Typography>

        <div className="space-y-4">
          {media.viewUrl && (
            <div className="mb-4">
              <img
                src={media.viewUrl}
                alt={media.name}
                className="max-w-full h-auto max-h-64 object-contain"
              />
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Typography variant="body2" className="text-gray-500">
                {t('admin:data.media.name', 'Tên file')}
              </Typography>
              <Typography>{media.name}</Typography>
            </div>

            <div>
              <Typography variant="body2" className="text-gray-500">
                {t('admin:data.media.author', 'Người tạo')}
              </Typography>
              <Typography>{media.author}</Typography>
            </div>

            <div className="col-span-2">
              <Typography variant="body2" className="text-gray-500">
                {t('admin:data.media.descriptionDetail', 'Mô tả')}
              </Typography>
              <Typography>{media.description}</Typography>
            </div>

            <div className="col-span-2">
              <Typography variant="body2" className="text-gray-500">
                {t('admin:data.media.storageKey', 'Khóa lưu trữ')}
              </Typography>
              <Typography className="break-all">{media.storageKey}</Typography>
            </div>

            {media.tags && media.tags.length > 0 && (
              <div className="col-span-2">
                <Typography variant="body2" className="text-gray-500">
                  {t('admin:data.media.tags', 'Thẻ')}
                </Typography>
                <div className="flex flex-wrap gap-2 mt-1">
                  {media.tags.map((tag, index) => (
                    <Chip key={index} size="sm" variant="default">
                      {tag}
                    </Chip>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex gap-2 justify-end w-full">
          <Button variant="outline" onClick={onClose}>
            {t('common:close', 'Đóng')}
          </Button>
        </div>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:data.media.confirmDelete', 'Xác nhận xóa media')}
        message={t(
          'admin:data.media.confirmDeleteMessage',
          'Bạn có chắc chắn muốn xóa media "{name}" không?',
          { name: media.name }
        )}
        itemName={media.name}
      />
    </>
  );
};

export default MediaDetailView;
