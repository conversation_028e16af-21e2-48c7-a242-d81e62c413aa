import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography, Button, Icon, Card, Loading, Table, Alert } from '@/shared/components/common';

import { useTheme, useChatPanel } from '@/shared/contexts';
import { useCartManager } from '../hooks/useCartApi';
// import { mockRelatedProducts } from '../data/mockData'; // Removed mock data
import CartSummary from '../components/CartSummary';
// import ProductCard from '../components/ProductCard'; // Unused for now
import { formatPrice } from '../utils/price-formatter';

/**
 * Trang giỏ hàng hiển thị danh sách sản phẩm đã thêm vào giỏ
 */
const CartPage: React.FC = () => {
  const { t } = useTranslation(['marketplace', 'common']);
  const navigate = useNavigate();
  useTheme(); // Sử dụng hook theme
  const { isChatPanelOpen } = useChatPanel(); // Kiểm tra trạng thái chat panel

  // Sử dụng hook quản lý giỏ hàng với real API
  const {
    cart,
    isLoading: isLoadingCart,
    error: cartError,
    removeCartItem,
    processPayment,
    isRemovingFromCart,
    isProcessingPayment,
  } = useCartManager();

  // State cho selected items (client-side)
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  // State cho local quantity changes (không gọi API)
  const [localQuantities, setLocalQuantities] = useState<Map<number, number>>(new Map());

  // Function để update quantity locally (không gọi API)
  const updateLocalQuantity = useCallback((cartItemId: number, change: number) => {
    setLocalQuantities(prev => {
      const newMap = new Map(prev);
      const currentItem = cart?.items.find(item => item.cartItemId === cartItemId);
      if (!currentItem) return prev;

      const currentLocalQuantity = newMap.get(cartItemId) ?? currentItem.quantity;
      const newQuantity = Math.max(1, currentLocalQuantity + change);

      newMap.set(cartItemId, newQuantity);
      return newMap;
    });
  }, [cart?.items]);

  // Helper function để lấy quantity hiện tại (local hoặc từ server)
  const getCurrentQuantity = useCallback((cartItemId: number) => {
    return localQuantities.get(cartItemId) ?? cart?.items.find(item => item.cartItemId === cartItemId)?.quantity ?? 1;
  }, [localQuantities, cart?.items]);

  // Tính tổng số lượng items bao gồm local changes
  const getTotalItemsWithLocalChanges = useCallback(() => {
    if (!cart?.items) return 0;
    return cart.items.reduce((sum, item) => {
      const currentQuantity = getCurrentQuantity(item.cartItemId);
      return sum + currentQuantity;
    }, 0);
  }, [cart?.items, getCurrentQuantity]);

  // Tính toán tổng tiền cho selected items (sử dụng local quantities)
  const calculateSelectedTotals = () => {
    if (!cart?.items) return { subtotal: 0, discount: 0, total: 0 };

    const selectedCartItems = cart.items.filter(item =>
      selectedItems.includes(item.cartItemId)
    );

    const subtotal = selectedCartItems.reduce((sum, item) => {
      const currentQuantity = getCurrentQuantity(item.cartItemId);
      return sum + (item.discountedPrice * currentQuantity);
    }, 0);

    return {
      subtotal,
      discount: 0, // No discount for now
      total: subtotal
    };
  };

  const totals = calculateSelectedTotals();

  // Debug log để kiểm tra cart data
  const totalItemsWithLocal = getTotalItemsWithLocalChanges();
  try {
    console.log('🛒 [CartPage] Debug:', {
      serverItems: cart?.items?.length || 0,
      serverTotalQuantity: cart?.items?.reduce((sum, item) => sum + item.quantity, 0) || 0,
      localChanges: Array.from(localQuantities.entries()),
      totalWithLocalChanges: totalItemsWithLocal,
      selectedItems: selectedItems.length
    });
  } catch (error) {
    console.error('Error in CartPage debug:', error);
  }

  // Cấu hình cột cho bảng
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(selectedItems.map(String));

  // Định nghĩa kiểu dữ liệu cho record trong bảng
  interface CartItemRecord {
    cartItemId: number;
    productId: number;
    productName: string;
    discountedPrice: number;
    quantity: number;
    sellerName: string;
    createdAt: number;
    totalPrice: number;
  }

  const columns = [
    {
      key: 'product',
      title: t('marketplace:cart.product', 'Sản phẩm'),
      dataIndex: 'productName',
      render: (_: unknown, record: CartItemRecord) => (
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 flex-shrink-0 rounded-md overflow-hidden bg-gray-100">
            <div className="w-full h-full flex items-center justify-center">
              <Icon name="image" size="md" className="text-gray-400" />
            </div>
          </div>
          <div className="flex-1">
            <Typography
              variant="body2"
              className="mb-1 line-clamp-2 h-10 overflow-hidden"
              title={record.productName}
            >
              {record.productName}
            </Typography>
            <Typography variant="caption" color="muted" className="block">
              Người bán: {record.sellerName}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      title: t('marketplace:cart.price', 'Giá'),
      dataIndex: 'discountedPrice',
      align: 'center' as const,
      render: (price: unknown) => (
        <div className="flex items-center justify-center">
          <Typography variant="body2" className="mr-1">
            {formatPrice(price as number)}
          </Typography>
          <Icon name="rpoint" size="md" className="text-red-600" />
        </div>
      ),
    },
    {
      key: 'quantity',
      title: t('marketplace:cart.quantity', 'Số lượng'),
      dataIndex: 'quantity',
      align: 'center' as const,
      render: (_: unknown, record: CartItemRecord) => {
        const currentQuantity = getCurrentQuantity(record.cartItemId);
        return (
          <div className="flex items-center justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateLocalQuantity(record.cartItemId, -1)}
              className="flex items-center justify-center border-none"
              disabled={currentQuantity <= 1}
            >
              <Icon name="minus" size="sm" />
            </Button>
            <Typography variant="body2" className="mx-1 w-4 text-center">
              {currentQuantity}
            </Typography>
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateLocalQuantity(record.cartItemId, 1)}
              className="flex items-center justify-center border-none"
            >
              <Icon name="plus" size="sm" />
            </Button>
          </div>
        );
      },
    },
    {
      key: 'total',
      title: t('marketplace:cart.total', 'Thành tiền'),
      dataIndex: 'totalPrice',
      align: 'center' as const,
      render: (_: unknown, record: CartItemRecord) => {
        const currentQuantity = getCurrentQuantity(record.cartItemId);
        const totalPrice = record.discountedPrice * currentQuantity;
        return (
          <div className="flex items-center justify-center">
            <Typography variant="body2" className="mr-1">
              {formatPrice(totalPrice)}
            </Typography>
            <Icon name="rpoint" size="md" className="text-red-600" />
          </div>
        );
      },
    },
    {
      key: 'actions',
      title: t('marketplace:cart.actions', 'Thao tác'),
      align: 'center' as const,
      render: (_: unknown, record: CartItemRecord) => (
        <button
          type="button"
          onClick={() => removeCartItem(record.cartItemId)}
          className="text-red-600 hover:text-red-800 dark:hover:text-red-400"
          disabled={isRemovingFromCart}
        >
          <Icon name="trash" size="sm" />
        </button>
      ),
    },
  ];

  // Chuyển đổi dữ liệu từ cart items sang format cho Table
  const tableData: CartItemRecord[] = cart?.items?.map(item => ({
    cartItemId: item.cartItemId,
    productId: item.productId,
    productName: item.productName,
    discountedPrice: item.discountedPrice,
    quantity: item.quantity,
    sellerName: item.sellerName,
    createdAt: item.createdAt,
    totalPrice: item.discountedPrice * item.quantity,
  })) || [];

  // Xử lý khi thay đổi lựa chọn hàng
  const handleSelectionChange = (keys: React.Key[]) => {
    setSelectedRowKeys(keys);
    // Cập nhật selectedItems
    const newSelectedItems = keys.map(key => Number(key));
    setSelectedItems(newSelectedItems);
  };

  // Xử lý khi click vào category (unused for now)
  // const handleCategoryClick = (categorySlug: string) => {
  //   navigate(`/marketplace/category/${categorySlug}`);
  // };

  // Xử lý checkout
  const handleCheckout = () => {
    if (selectedItems.length === 0) {
      return;
    }

    const selectedProductIds = selectedItems.map(cartItemId => {
      const item = cart?.items.find(item => item.cartItemId === cartItemId);
      return item?.productId;
    }).filter(Boolean) as number[];

    processPayment(selectedProductIds);
  };

  // Hiển thị loading
  if (isLoadingCart) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  // Hiển thị lỗi
  if (cartError) {
    return (
      <Alert
        type="error"
        title="Lỗi tải giỏ hàng"
        message={cartError.message}
      />
    );
  }

  // Hiển thị giỏ hàng trống
  if (!cart?.items || cart.items.length === 0) {
    return (
      <div
        className="max-w-3xl mx-auto shadow-none my-8 py-12 animate-fade-in"
      >
        <div className="flex flex-col items-center justify-center space-y-8 text-center px-4">
          {/* Icon với hiệu ứng */}
          <div className="relative">
            <div className="w-32 h-32 rounded-full bg-red-50 dark:bg-gray-800 flex items-center justify-center mb-2 animate-pulse-slow">
              <Icon name="shopping-cart" size="xl" className="text-red-500 dark:text-red-400 transform scale-150" />
            </div>
            <div className="absolute -top-2 -right-2 w-8 h-8 rounded-full bg-red-100 dark:bg-gray-700 flex items-center justify-center animate-bounce">
              <Icon name="x" size="sm" className="text-red-500 dark:text-red-400" />
            </div>
          </div>

          {/* Nội dung */}
          <div className="max-w-md space-y-3">
            <Typography variant="h4" weight="bold" className="mb-2">
              {t('marketplace:cart.empty', 'Giỏ hàng của bạn đang trống')}
            </Typography>

            <Typography variant="body1" color="muted" className="mb-6">
              {t(
                'marketplace:cart.emptyMessage',
                'Hãy thêm sản phẩm vào giỏ hàng để tiếp tục mua sắm.'
              )}
            </Typography>
          </div>



          {/* Nút hành động */}
          <div className="pt-4">
            <Button
              variant="primary"
              leftIcon={<Icon name="arrow-left" />}
              onClick={() => navigate('/marketplace')}
              className="bg-red-600 hover:bg-red-700 transform transition-transform hover:scale-105"
              size="lg"
            >
              {t('marketplace:cart.continueShopping', 'Tiếp tục mua sắm')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Bảng sản phẩm - 8 cột */}
        <div className="w-full lg:w-2/3">
          <Card className="overflow-hidden">
            <Table<CartItemRecord>
              data={tableData}
              columns={columns}
              rowSelection={{
                selectedRowKeys,
                onChange: handleSelectionChange,
              }}
              rowKey="cartItemId"
              bordered
              hoverable
            />
          </Card>
        </div>

        {/* Thanh toán - 4 cột */}
        <div className="w-full lg:w-1/3">
          <CartSummary
            totalItems={selectedItems.length}
            subtotal={totals.subtotal}
            discount={totals.discount}
            total={totals.total}
            onCheckout={handleCheckout}
            isLoading={isProcessingPayment}
            selectedCount={selectedItems.length}
          />
        </div>
      </div>

      {/* Sản phẩm liên quan - Cuộn ngang */}
      <div className="mt-12">
        <Typography variant="h5" className="mb-6">
          {t('marketplace:product.relatedProducts', 'Sản phẩm liên quan')}
        </Typography>

        {/* Grid hiển thị 2 hoặc 3 sản phẩm tùy thuộc vào trạng thái chat panel */}
        <div
          className={`grid grid-cols-1 sm:grid-cols-2 ${isChatPanelOpen ? 'lg:grid-cols-2' : 'lg:grid-cols-3'} gap-4`}
        >
          {/* TODO: Implement related products from API */}
          <div className="text-center py-8">
            <Typography variant="body2" color="muted">
              Sản phẩm liên quan sẽ được hiển thị ở đây
            </Typography>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
