import { useQueryClient } from '@tanstack/react-query';
import { useApiQuery, useApiMutation } from '@/shared/api/hooks';
import { knowledgeFileService } from '../services/knowledge-file.service';
import {
  CreateKnowledgeFileDto,
  KnowledgeFileListResponse,
  KnowledgeFileQueryParams,
  ApiResponse,
} from '../types';

// Định nghĩa các query key
export const KNOWLEDGE_FILE_QUERY_KEYS = {
  all: ['admin', 'knowledge-files'] as const,
  lists: () => [...KNOWLEDGE_FILE_QUERY_KEYS.all, 'list'] as const,
  list: (filters: KnowledgeFileQueryParams) =>
    [...KNOWLEDGE_FILE_QUERY_KEYS.lists(), filters] as const,
  details: () => [...KNOWLEDGE_FILE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...KNOWLEDGE_FILE_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách file tri thức
 * @param params Tham số truy vấn
 * @returns Kết quả truy vấn danh sách file tri thức
 */
export const useKnowledgeFiles = (params?: KnowledgeFileQueryParams) => {
  return useApiQuery<KnowledgeFileListResponse>(
    KNOWLEDGE_FILE_QUERY_KEYS.list(params || {}),
    '/admin/knowledge-files',
    {
      params,
    }
  );
};

/**
 * Hook để tạo nhiều file tri thức mới
 * @returns Mutation để tạo file tri thức
 */
export const useCreateKnowledgeFiles = () => {
  const queryClient = useQueryClient();

  return useApiMutation<ApiResponse, { files: CreateKnowledgeFileDto[] }>(
    '/admin/knowledge-files/batch',
    {
      onSuccess: () => {
        // Làm mới danh sách file tri thức sau khi tạo thành công
        queryClient.invalidateQueries({ queryKey: KNOWLEDGE_FILE_QUERY_KEYS.lists() });
      },
      onError: error => {
        console.error('[useCreateKnowledgeFiles] Error creating knowledge files:', error);
      },
    }
  );
};

/**
 * Hook để xóa một hoặc nhiều file tri thức
 * @returns Mutation để xóa file tri thức
 */
export const useDeleteKnowledgeFile = () => {
  const queryClient = useQueryClient();

  return {
    deleteFiles: async (fileIds: string | string[]) => {
      // Convert fileId to array if only one ID is passed
      const ids = Array.isArray(fileIds) ? fileIds : [fileIds];

      // Call service to delete files
      const result = await knowledgeFileService.deleteKnowledgeFiles(ids);

      // Refresh knowledge files list after successful deletion
      queryClient.invalidateQueries({ queryKey: KNOWLEDGE_FILE_QUERY_KEYS.lists() });

      return result;
    },
  };
};
