import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';
import i18n from '@/lib/i18n';

// Lazy load pages
const ExternalAgentsPage = lazy(() => import('../pages/ExternalAgentsPage'));
const AgentDetailPage = lazy(() => import('../pages/AgentDetailPage'));
const AgentTestingPage = lazy(() => import('../pages/AgentTestingPage'));
const AgentAnalyticsPage = lazy(() => import('../pages/AgentAnalyticsPage'));
const MessageHistoryPage = lazy(() => import('../pages/MessageHistoryPage'));

const t = (key: string, defaultValue?: string) => i18n.t(key, { defaultValue });

/**
 * External Agents module routes
 */
const externalAgentRoutes: RouteObject[] = [
  // Main agents list page
  {
    path: '/external-agents',
    element: (
      <MainLayout title={t('external-agents:title', 'External Agents')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Agent detail page
  {
    path: '/external-agents/:id',
    element: (
      <MainLayout title={t('external-agents:agent.title', 'Agent Details')}>
        <Suspense fallback={<Loading />}>
          <AgentDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Agent testing page
  {
    path: '/external-agents/:id/testing',
    element: (
      <MainLayout title={t('external-agents:connection.test', 'Agent Testing')}>
        <Suspense fallback={<Loading />}>
          <AgentTestingPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Agent analytics page
  {
    path: '/external-agents/:id/analytics',
    element: (
      <MainLayout title={t('external-agents:navigation.analytics', 'Agent Analytics')}>
        <Suspense fallback={<Loading />}>
          <AgentAnalyticsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Message history page
  {
    path: '/external-agents/:id/messages',
    element: (
      <MainLayout title={t('external-agents:navigation.messages', 'Message History')}>
        <Suspense fallback={<Loading />}>
          <MessageHistoryPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Protocols overview page
  {
    path: '/external-agents/protocols',
    element: (
      <MainLayout title={t('external-agents:navigation.protocols', 'Protocols')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Protocol templates page
  {
    path: '/external-agents/templates',
    element: (
      <MainLayout title={t('external-agents:navigation.templates', 'Protocol Templates')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Webhooks management page
  {
    path: '/external-agents/webhooks',
    element: (
      <MainLayout title={t('external-agents:navigation.webhooks', 'Webhooks')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Analytics overview page
  {
    path: '/external-agents/analytics',
    element: (
      <MainLayout title={t('external-agents:navigation.analytics', 'Analytics')}>
        <Suspense fallback={<Loading />}>
          <AgentAnalyticsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Settings page
  {
    path: '/external-agents/settings',
    element: (
      <MainLayout title={t('external-agents:navigation.settings', 'Settings')}>
        <Suspense fallback={<Loading />}>
          <ExternalAgentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default externalAgentRoutes;
