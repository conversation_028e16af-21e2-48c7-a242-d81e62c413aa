/* Animation definitions */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes menuItemAppear {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  70% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes menuIconPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes slideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(10px);
    opacity: 0;
  }
}

/* <PERSON><PERSON>m bảo ActionMenu luôn nổi trên các element khác */
.action-menu-container {
  position: relative;
  z-index: 10;
}

.action-menu-container .modern-menu {
  z-index: 100000 !important;
}

/* <PERSON><PERSON><PERSON> bảo table cell không ảnh hưởng đến positioning */
.table-action-cell {
  position: relative;
  overflow: visible !important;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes floatButton {
  0% {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: translateY(0);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gentleSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-in-out;
}

.animate-slide-out {
  animation: slideOut 0.3s ease-in-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.3s ease-in-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}

.animate-menu-item {
  animation: menuItemAppear 0.5s ease-out forwards;
}

.animate-menu-icon {
  animation: menuIconPulse 1s ease-in-out infinite;
}

.animate-float {
  animation: floatButton 3s ease-in-out infinite;
}

.animate-spin {
  animation: spin 2s linear infinite;
}

.animate-gentle-spin {
  animation: gentleSpin 8s linear infinite;
}

.animate-reverse-spin {
  animation: spin 2s linear infinite reverse;
}

/* Animation durations */
.duration-100 {
  animation-duration: 100ms;
}

.duration-200 {
  animation-duration: 200ms;
}

.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.duration-700 {
  animation-duration: 700ms;
}

.duration-1000 {
  animation-duration: 1000ms;
}

/* Animation delays */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Animation fill modes */
.fill-forwards {
  animation-fill-mode: forwards;
}

.fill-backwards {
  animation-fill-mode: backwards;
}

.fill-both {
  animation-fill-mode: both;
}

/* Animation timing functions */
.ease-linear {
  animation-timing-function: linear;
}

.ease-in {
  animation-timing-function: ease-in;
}

.ease-out {
  animation-timing-function: ease-out;
}

.ease-in-out {
  animation-timing-function: ease-in-out;
}

/* Animation iterations */
.iterate-infinite {
  animation-iteration-count: infinite;
}

.iterate-1 {
  animation-iteration-count: 1;
}

.iterate-2 {
  animation-iteration-count: 2;
}

.iterate-3 {
  animation-iteration-count: 3;
}

/* Page transition animations */
.page-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition:
    opacity 300ms,
    transform 300ms;
}

.page-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateY(10px);
  transition:
    opacity 300ms,
    transform 300ms;
}

/* Fix for FormSection border-radius when collapsed */
.section-with-bottom-radius {
  position: relative;
  overflow: hidden;
}

/* Sử dụng một cách tiếp cận khác để giữ border-radius */
.section-collapsed {
  border-radius: 0.5rem !important;
}
