import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Checkbox } from '@/shared/components/common';
import { CrawlUrlParams } from '@/modules/admin/data/url/types/url.types';

interface CrawlUrlFormProps {
  crawlParams: CrawlUrlParams;
  onParamsChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const CrawlUrlForm: React.FC<CrawlUrlFormProps> = ({
  crawlParams,
  onParamsChange,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <div className="h-full flex flex-col">
      {/* Form Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              URL <span className="text-red-500">*</span>
            </label>
            <input
              type="url"
              name="url"
              value={crawlParams.url}
              onChange={onParamsChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://example.com"
              required
            />
          </div>

          {/* Depth Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('admin:data.url.crawlDepth', 'Độ sâu')} (1-5)
            </label>
            <input
              type="number"
              name="depth"
              value={crawlParams.depth}
              onChange={onParamsChange}
              min={1}
              max={5}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              {t('admin:data.url.crawlDepthHelp', 'Số cấp độ liên kết sẽ được crawl')}
            </p>
          </div>

          {/* Max URLs Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('admin:data.url.maxUrls', 'Số URL tối đa')}
            </label>
            <input
              type="number"
              name="maxUrls"
              value={crawlParams.maxUrls}
              onChange={onParamsChange}
              min={1}
              max={10000}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="mt-1 text-sm text-gray-500">
              {t('admin:data.url.maxUrlsHelp', 'Giới hạn số lượng URL sẽ được crawl')}
            </p>
          </div>

          {/* Ignore Robots.txt Checkbox */}
          <div className="flex items-start space-x-3">
            <Checkbox
              checked={crawlParams.ignoreRobotsTxt}
              onChange={checked => {
                const event = {
                  target: {
                    name: 'ignoreRobotsTxt',
                    type: 'checkbox',
                    checked,
                  },
                } as React.ChangeEvent<HTMLInputElement>;
                onParamsChange(event);
              }}
              color="danger"
              variant="filled"
            />
            <div>
              <label className="text-sm font-medium text-gray-700">
                {t('admin:data.url.ignoreRobots', 'Bỏ qua robots.txt')}
              </label>
              <p className="text-sm text-gray-500">
                {t('admin:data.url.ignoreRobotsHelp', 'Crawl mà không tuân thủ quy tắc robots.txt')}
              </p>
            </div>
          </div>
        </form>
      </div>

      {/* Footer */}
      <div className="flex justify-end space-x-3 p-6 ">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          {t('admin:data.url.cancel', 'Hủy')}
        </Button>
        <Button variant="primary" onClick={onSubmit} disabled={!crawlParams.url || isLoading}>
          {isLoading
            ? t('admin:data.url.processing', 'Đang xử lý...')
            : t('admin:data.url.startCrawl', 'Bắt đầu Crawl')}
        </Button>
      </div>
    </div>
  );
};

export default CrawlUrlForm;
