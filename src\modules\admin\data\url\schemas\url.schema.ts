import { z } from 'zod';
import { TFunction } from 'i18next';

/**
 * Schema factory cho URL với hỗ trợ i18n
 */
export const createUrlSchemas = (t: TFunction) => {
  /**
   * Schema cho URL
   */
  const urlSchema = z.object({
    id: z.string(),
    url: z.string().url({ message: t('admin:data.url.validation.invalidUrl', 'URL không hợp lệ') }),
    title: z.string().min(1, { message: t('admin:data.url.validation.titleRequired', 'Tiêu đề không được để trống') }),
    content: z.string().min(1, { message: t('admin:data.url.validation.contentRequired', 'Nội dung không được để trống') }),
    type: z.string().optional(),
    tags: z.array(z.string()).optional(),
    ownedBy: z.number(),
    createdAt: z.number(),
    updatedAt: z.number(),
    isActive: z.boolean(),
  });

  /**
   * Schema cho tạo URL mới
   */
  const createUrlSchema = z.object({
    url: z.string().url({ message: t('admin:data.url.validation.invalidUrl', 'URL không hợp lệ') }),
    title: z.string().min(1, { message: t('admin:data.url.validation.titleRequired', 'Tiêu đề không được để trống') }),
    content: z.string().min(1, { message: t('admin:data.url.validation.contentRequired', 'Nội dung không được để trống') }),
    type: z.string().optional(),
    tags: z.array(z.string()).optional(),
    ownedBy: z.number(),
    isActive: z.boolean().optional().default(true),
  });

  /**
   * Schema cho cập nhật URL
   */
  const updateUrlSchema = z.object({
    url: z.string().url({ message: t('admin:data.url.validation.invalidUrl', 'URL không hợp lệ') }).optional(),
    title: z.string().min(1, { message: t('admin:data.url.validation.titleRequired', 'Tiêu đề không được để trống') }).optional(),
    content: z.string().min(1, { message: t('admin:data.url.validation.contentRequired', 'Nội dung không được để trống') }).optional(),
    type: z.string().optional(),
    tags: z.array(z.string()).optional(),
    ownedBy: z.number().optional(),
    isActive: z.boolean().optional(),
  });

  /**
   * Schema cho tìm kiếm URL
   */
  const searchUrlSchema = z.object({
    page: z.number().optional().default(1),
    limit: z.number().optional().default(10),
    sortBy: z.string().optional().default('createdAt'),
    sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC'),
    keyword: z.string().optional(),
    type: z.string().optional(),
    tags: z.array(z.string()).optional(),
    userId: z.number().optional(),
    isActive: z.boolean().optional(),
  });

  /**
   * Schema cho crawl URL
   */
  const crawlUrlSchema = z.object({
    url: z.string().url({ message: t('admin:data.url.validation.invalidUrl', 'URL không hợp lệ') }),
    depth: z.number().min(1).max(5).default(1),
    ignoreRobotsTxt: z.boolean().optional().default(false),
    maxUrls: z.number().min(1).max(100).optional().default(20),
    ownedBy: z.number().optional(),
  });

  return {
    urlSchema,
    createUrlSchema,
    updateUrlSchema,
    searchUrlSchema,
    crawlUrlSchema,
  };
};

// Export legacy schemas for backward compatibility (deprecated)
export const urlSchema = z.object({
  id: z.string(),
  url: z.string().url({ message: 'URL không hợp lệ' }),
  title: z.string().min(1, { message: 'Tiêu đề không được để trống' }),
  content: z.string().min(1, { message: 'Nội dung không được để trống' }),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  ownedBy: z.number(),
  createdAt: z.number(),
  updatedAt: z.number(),
  isActive: z.boolean(),
});

export const createUrlSchema = z.object({
  url: z.string().url({ message: 'URL không hợp lệ' }),
  title: z.string().min(1, { message: 'Tiêu đề không được để trống' }),
  content: z.string().min(1, { message: 'Nội dung không được để trống' }),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  ownedBy: z.number(),
  isActive: z.boolean().optional().default(true),
});

export const updateUrlSchema = z.object({
  url: z.string().url({ message: 'URL không hợp lệ' }).optional(),
  title: z.string().min(1, { message: 'Tiêu đề không được để trống' }).optional(),
  content: z.string().min(1, { message: 'Nội dung không được để trống' }).optional(),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  ownedBy: z.number().optional(),
  isActive: z.boolean().optional(),
});

export const searchUrlSchema = z.object({
  page: z.number().optional().default(1),
  limit: z.number().optional().default(10),
  sortBy: z.string().optional().default('createdAt'),
  sortDirection: z.enum(['ASC', 'DESC']).optional().default('DESC'),
  keyword: z.string().optional(),
  type: z.string().optional(),
  tags: z.array(z.string()).optional(),
  userId: z.number().optional(),
  isActive: z.boolean().optional(),
});

export const crawlUrlSchema = z.object({
  url: z.string().url({ message: 'URL không hợp lệ' }),
  depth: z.number().min(1).max(5).default(1),
  ignoreRobotsTxt: z.boolean().optional().default(false),
  maxUrls: z.number().min(1).max(100).optional().default(20),
  ownedBy: z.number().optional(),
});
