import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Form,
  FormItem,
  Input,
  Table,
  Badge,
  Alert
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import {
  ShippingAddress,
  ShippingRate
} from '../types';
import {
  useShippingProviderConfigurations,
  useCalculateShippingRates
} from '../hooks';

interface RateCalculationRequest {
  fromAddress: ShippingAddress;
  toAddress: ShippingAddress;
  weight: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

interface TableRecord extends ShippingRate {
  providerName: string;
  key: string;
}

interface ShippingRateCalculatorProps {
  onClose?: () => void;
}

/**
 * Component tính toán và so sánh phí vận chuyển
 */
const ShippingRateCalculator: React.FC<ShippingRateCalculatorProps> = ({
  onClose
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const { formRef } = useFormErrors<RateCalculationRequest>();
  
  // State
  const [formData, setFormData] = useState<RateCalculationRequest>({
    fromAddress: {
      name: '',
      phone: '',
      address: '',
      wardName: '',
      districtName: '',
      provinceName: '',
    },
    toAddress: {
      name: '',
      phone: '',
      address: '',
      wardName: '',
      districtName: '',
      provinceName: '',
    },
    weight: 1,
    dimensions: {
      length: 10,
      width: 10,
      height: 10,
    },
  });
  
  const [calculationResults, setCalculationResults] = useState<{
    providerId: number;
    providerName: string;
    rates: ShippingRate[];
    error?: string;
  }[]>([]);

  // Fetch providers and mutations
  const { data: providersResponse } = useShippingProviderConfigurations();
  const providers = providersResponse?.result?.items?.filter(p => p.isActive) || [];
  const calculateRatesMutation = useCalculateShippingRates();

  // Handle field changes
  const handleFieldChange = (field: string, value: string | number) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof RateCalculationRequest] as Record<string, string | number>),
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Calculate rates for all providers
  const handleCalculateRates = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    if (providers.length === 0) {
      formRef.current?.setErrors({ weight: 'No active shipping providers found' });
      return;
    }

    setCalculationResults([]);
    
    // Calculate rates for each provider
    const results = await Promise.allSettled(
      providers.map(async (provider) => {
        try {
          const response = await calculateRatesMutation.mutateAsync({
            id: provider.id,
            rateRequest: formData
          });
          
          return {
            providerId: provider.id,
            providerName: provider.providerName,
            rates: response.result || [],
          };
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to calculate rates';
          return {
            providerId: provider.id,
            providerName: provider.providerName,
            rates: [],
            error: errorMessage,
          };
        }
      })
    );

    // Process results
    const processedResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        const errorMessage = result.reason instanceof Error ? result.reason.message : 'Failed to calculate rates';
        return {
          providerId: providers[index].id,
          providerName: providers[index].providerName,
          rates: [],
          error: errorMessage,
        };
      }
    });

    setCalculationResults(processedResults);
  };

  // Get best rate
  const getBestRate = () => {
    const allRates = calculationResults
      .filter(result => !result.error && result.rates.length > 0)
      .flatMap(result => 
        result.rates.map(rate => ({
          ...rate,
          providerName: result.providerName
        }))
      );
    
    if (allRates.length === 0) return null;
    
    return allRates.reduce((best, current) => 
      current.fee < best.fee ? current : best
    );
  };

  // Table columns for rate comparison
  const columns = [
    {
      key: 'provider',
      title: 'Provider',
      dataIndex: 'providerName',
      render: (value: string) => (
        <Typography variant="body2" className="font-medium">
          {value}
        </Typography>
      ),
    },
    {
      key: 'service',
      title: 'Service',
      dataIndex: 'serviceName',
      render: (value: string) => (
        <Typography variant="body2">{value}</Typography>
      ),
    },
    {
      key: 'fee',
      title: 'Fee',
      dataIndex: 'fee',
      render: (value: number, record: TableRecord) => {
        const bestRate = getBestRate();
        const isBest = bestRate && record.fee === bestRate.fee && record.serviceName === bestRate.serviceName;
        
        return (
          <div className="flex items-center gap-2">
            <Typography variant="body2" className="font-semibold">
              {value.toLocaleString()} VND
            </Typography>
            {isBest && (
              <Badge variant="success" size="sm">
                Best
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      key: 'delivery',
      title: 'Estimated Delivery',
      dataIndex: 'estimatedDelivery',
      render: (value: string) => (
        <Typography variant="body2">{value}</Typography>
      ),
    },
  ];

  // Prepare table data
  const tableData = calculationResults
    .filter(result => !result.error && result.rates.length > 0)
    .flatMap(result => 
      result.rates.map(rate => ({
        ...rate,
        providerName: result.providerName,
        key: `${result.providerId}-${rate.serviceId}`,
      }))
    );

  return (
    <Card className="w-full max-w-6xl">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Icon name="calculator" size="lg" className="text-primary" />
            <div>
              <Typography variant="h3">
                {t('integration:shipping.rateCalculator', 'Rate Calculator')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                Compare shipping rates across providers
              </Typography>
            </div>
          </div>
          
          {onClose && (
            <Button variant="ghost" onClick={onClose}>
              <Icon name="x" size="sm" />
            </Button>
          )}
        </div>

        {/* Calculation Form */}
        <Form ref={formRef as never} onSubmit={handleCalculateRates as never} className="space-y-6 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* From Address */}
            <div className="space-y-4">
              <Typography variant="h6">From Address</Typography>
              
              <FormItem label="Address" name="fromAddress.address" required>
                <Input
                  value={formData.fromAddress.address}
                  onChange={(e) => handleFieldChange('fromAddress.address', e.target.value)}
                  placeholder="Pickup address"
                />
              </FormItem>
              
              <div className="grid grid-cols-3 gap-3">
                <FormItem label="Ward" name="fromAddress.wardName">
                  <Input
                    value={formData.fromAddress.wardName}
                    onChange={(e) => handleFieldChange('fromAddress.wardName', e.target.value)}
                    placeholder="Ward"
                  />
                </FormItem>
                
                <FormItem label="District" name="fromAddress.districtName">
                  <Input
                    value={formData.fromAddress.districtName}
                    onChange={(e) => handleFieldChange('fromAddress.districtName', e.target.value)}
                    placeholder="District"
                  />
                </FormItem>
                
                <FormItem label="Province" name="fromAddress.provinceName">
                  <Input
                    value={formData.fromAddress.provinceName}
                    onChange={(e) => handleFieldChange('fromAddress.provinceName', e.target.value)}
                    placeholder="Province"
                  />
                </FormItem>
              </div>
            </div>

            {/* To Address */}
            <div className="space-y-4">
              <Typography variant="h6">To Address</Typography>
              
              <FormItem label="Address" name="toAddress.address" required>
                <Input
                  value={formData.toAddress.address}
                  onChange={(e) => handleFieldChange('toAddress.address', e.target.value)}
                  placeholder="Delivery address"
                />
              </FormItem>
              
              <div className="grid grid-cols-3 gap-3">
                <FormItem label="Ward" name="toAddress.wardName">
                  <Input
                    value={formData.toAddress.wardName}
                    onChange={(e) => handleFieldChange('toAddress.wardName', e.target.value)}
                    placeholder="Ward"
                  />
                </FormItem>
                
                <FormItem label="District" name="toAddress.districtName">
                  <Input
                    value={formData.toAddress.districtName}
                    onChange={(e) => handleFieldChange('toAddress.districtName', e.target.value)}
                    placeholder="District"
                  />
                </FormItem>
                
                <FormItem label="Province" name="toAddress.provinceName">
                  <Input
                    value={formData.toAddress.provinceName}
                    onChange={(e) => handleFieldChange('toAddress.provinceName', e.target.value)}
                    placeholder="Province"
                  />
                </FormItem>
              </div>
            </div>
          </div>

          {/* Package Details */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <FormItem label="Weight (kg)" name="weight" required>
              <Input
                type="number"
                value={formData.weight}
                onChange={(e) => handleFieldChange('weight', parseFloat(e.target.value))}
                placeholder="Weight"
                min="0.1"
                step="0.1"
              />
            </FormItem>
            
            <FormItem label="Length (cm)" name="dimensions.length">
              <Input
                type="number"
                value={formData.dimensions?.length || ''}
                onChange={(e) => handleFieldChange('dimensions.length', parseFloat(e.target.value))}
                placeholder="Length"
                min="1"
              />
            </FormItem>
            
            <FormItem label="Width (cm)" name="dimensions.width">
              <Input
                type="number"
                value={formData.dimensions?.width || ''}
                onChange={(e) => handleFieldChange('dimensions.width', parseFloat(e.target.value))}
                placeholder="Width"
                min="1"
              />
            </FormItem>
            
            <FormItem label="Height (cm)" name="dimensions.height">
              <Input
                type="number"
                value={formData.dimensions?.height || ''}
                onChange={(e) => handleFieldChange('dimensions.height', parseFloat(e.target.value))}
                placeholder="Height"
                min="1"
              />
            </FormItem>
          </div>

          {/* Calculate Button */}
          <div className="flex justify-center">
            <Button
              type="submit"
              variant="primary"
              size="lg"
              isLoading={calculateRatesMutation.isPending}
              leftIcon={<Icon name="calculator" size="sm" />}
            >
              Calculate Rates
            </Button>
          </div>
        </Form>

        {/* Results */}
        {calculationResults.length > 0 && (
          <div className="space-y-6">
            <Typography variant="h5">Rate Comparison Results</Typography>
            
            {/* Error alerts */}
            {calculationResults.filter(r => r.error).map(result => (
              <Alert
                key={result.providerId}
                type="error"
                title={`${result.providerName} Error`}
                message={result.error || 'Unknown error'}
              />
            ))}
            
            {/* Results table */}
            {tableData.length > 0 ? (
              <Table
                columns={columns as never}
                data={tableData}
                rowKey="key"
                bordered
                striped
              />
            ) : (
              <Alert
                type="warning"
                title="No Rates Available"
                message="No shipping rates could be calculated for the provided addresses."
              />
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default ShippingRateCalculator;
