import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  EmptyState,
  ResponsiveGrid,
  Badge,
  Modal
} from '@/shared/components/common';
import { ShippingProviderConfiguration, ShippingProviderType } from '../types';
import {
  useShippingProviderConfigurations,
  useTestShippingProviderConfiguration,
  useUpdateShippingProviderConfiguration
} from '../hooks';
import ShippingTestConnection from './ShippingTestConnection';

interface ShippingProviderListProps {
  onCreateNew?: () => void;
  onEdit?: (provider: ShippingProviderConfiguration) => void;
  onTest?: (provider: ShippingProviderConfiguration) => void;
  onToggleStatus?: (provider: ShippingProviderConfiguration) => void;
}

/**
 * Component hiển thị danh sách nhà vận chuyển với status indicators
 */
const ShippingProviderList: React.FC<ShippingProviderListProps> = ({
  onCreateNew,
  onEdit,
  onTest,
  onToggleStatus
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // Fetch shipping providers
  const { data: providersResponse, isLoading } = useShippingProviderConfigurations();
  const providers = providersResponse?.result?.items || [];

  // State for test modal
  const [testingProvider, setTestingProvider] = useState<ShippingProviderConfiguration | null>(null);

  // Mutations
  const testProviderMutation = useTestShippingProviderConfiguration();
  const updateProviderMutation = useUpdateShippingProviderConfiguration();

  // Handle test provider
  const handleTestProvider = (provider: ShippingProviderConfiguration) => {
    setTestingProvider(provider);
    onTest?.(provider);
  };

  // Handle toggle status
  const handleToggleStatus = (provider: ShippingProviderConfiguration) => {
    updateProviderMutation.mutate({
      id: provider.id,
      data: { isActive: !provider.isActive }
    });
    onToggleStatus?.(provider);
  };

  // Helper functions
  const getProviderIcon = (type: ShippingProviderType): string => {
    switch (type) {
      case 'ghn':
        return 'truck';
      case 'ghtk':
        return 'package';
      case 'viettel-post':
        return 'mail';
      case 'vnpost':
        return 'send';
      default:
        return 'truck';
    }
  };

  const getProviderColor = (type: ShippingProviderType): string => {
    switch (type) {
      case 'ghn':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ghtk':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'viettel-post':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'vnpost':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Render provider card
  const renderProviderCard = (provider: ShippingProviderConfiguration) => (
    <Card
      key={provider.id}
      className="group hover:shadow-lg transition-all duration-200 cursor-pointer"
      hoverable
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${getProviderColor(provider.providerType)}`}>
              <Icon name={getProviderIcon(provider.providerType)} size="sm" />
            </div>
            <div>
              <Typography variant="h6" className="font-semibold">
                {provider.providerName}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {provider.providerType.toUpperCase()}
              </Typography>
            </div>
          </div>

          {/* Status badges */}
          <div className="flex flex-col items-end space-y-1">
            <Badge
              variant={provider.isActive ? 'success' : 'secondary'}
              size="sm"
            >
              {provider.isActive ? t('common:active') : t('common:inactive')}
            </Badge>
            {provider.isDefault && (
              <Badge variant="primary" size="sm">
                {t('integration:shipping.default')}
              </Badge>
            )}
          </div>
        </div>

        {/* API Key info */}
        <div className="mb-4">
          <Typography variant="body2" className="text-muted-foreground mb-1">
            API Key:
          </Typography>
          <Typography variant="body2" className="font-mono text-sm">
            {provider.apiKey}
          </Typography>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-border">
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleTestProvider(provider)}
              leftIcon={<Icon name="activity" size="xs" />}
              isLoading={testProviderMutation.isPending}
            >
              {t('integration:shipping.test')}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit?.(provider)}
              leftIcon={<Icon name="edit" size="xs" />}
            >
              {t('common:edit')}
            </Button>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleToggleStatus(provider)}
            leftIcon={
              <Icon
                name={provider.isActive ? 'pause' : 'play'}
                size="xs"
              />
            }
            isLoading={updateProviderMutation.isPending}
          >
            {provider.isActive ? t('common:disable') : t('common:enable')}
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <Card>
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                {t('integration:shipping.title', 'Quản lý Vận chuyển')}
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                {t('integration:shipping.description', 'Quản lý các tích hợp nhà vận chuyển')}
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              {t('integration:shipping.addProvider', 'Thêm nhà vận chuyển')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Providers List */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <div className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-32"></div>
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div className="space-y-2 mb-4">
                  <div className="h-3 bg-gray-200 rounded w-20"></div>
                  <div className="h-3 bg-gray-200 rounded w-40"></div>
                </div>
                <div className="flex space-x-2">
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : providers.length > 0 ? (
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
          gap={6}
        >
          {providers.map(renderProviderCard)}
        </ResponsiveGrid>
      ) : (
        <Card>
          <div className="p-8">
            <EmptyState
              icon="truck"
              title={t('integration:shipping.empty.title', 'Chưa có nhà vận chuyển nào')}
              description={t(
                'integration:shipping.empty.description',
                'Bạn chưa thêm nhà vận chuyển nào. Hãy thêm nhà vận chuyển đầu tiên.'
              )}
              actions={
                <Button
                  variant="primary"
                  onClick={onCreateNew}
                  leftIcon={<Icon name="plus" size="sm" />}
                >
                  {t('integration:shipping.addFirstProvider', 'Thêm nhà vận chuyển đầu tiên')}
                </Button>
              }
            />
          </div>
        </Card>
      )}

      {/* Test Connection Modal */}
      <Modal
        isOpen={!!testingProvider}
        onClose={() => setTestingProvider(null)}
        size="xl"
      >
        {testingProvider && (
          <ShippingTestConnection
            provider={testingProvider}
            onClose={() => setTestingProvider(null)}
          />
        )}
      </Modal>
    </div>
  );
};

export default ShippingProviderList;
