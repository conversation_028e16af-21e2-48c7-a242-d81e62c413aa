import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import i18n from '@/lib/i18n';
import { RouteGuard } from '@/shared/hoc';

// Import Marketplace pages
const ProductListPage = lazy(() => import('@/modules/marketplace/pages/ProductListPage'));
const ProductDetailPage = lazy(() => import('@/modules/marketplace/pages/ProductDetailPage'));
const CartPage = lazy(() => import('@/modules/marketplace/pages/CartPage'));
const ProductsForSalePage = lazy(() => import('@/modules/marketplace/pages/ProductsForSalePage'));
const PurchasedProductsPage = lazy(() => import('@/modules/marketplace/pages/PurchasedProductsPage'));

/**
 * Marketplace module routes
 * Sử dụng đa ngôn ngữ cho tất cả các route titles
 */
const marketplaceRoutes: RouteObject[] = [
  {
    path: '/marketplace',
    element: (
      <MainLayout title={i18n.t('marketplace:title', 'Sàn giao dịch')}>
        <Suspense fallback={<Loading />}>
          <ProductListPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/marketplace/product/:productId',
    element: (
      <MainLayout title={i18n.t('marketplace:product.detail.information', 'Chi tiết sản phẩm')}>
        <Suspense fallback={<Loading />}>
          <ProductDetailPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/marketplace/cart',
    element: (
      <MainLayout title={i18n.t('marketplace:cart.title', 'Giỏ hàng')}>
        <Suspense fallback={<Loading />}>
          <RouteGuard component={CartPage} type="PROTECT" />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/marketplace/products-for-sale',
    element: (
      <MainLayout title={i18n.t('marketplace:productsForSale.title', 'Sản phẩm đăng bán')}>
        <Suspense fallback={<Loading />}>
          <RouteGuard component={ProductsForSalePage} type="PROTECT" />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/marketplace/products-for-sale/edit/:productId',
    element: (
      <MainLayout title={i18n.t('marketplace:productsForSale.edit.title', 'Chỉnh sửa sản phẩm')}>
        <Suspense fallback={<Loading />}>
          <RouteGuard component={ProductsForSalePage} type="PROTECT" />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/marketplace/purchased-products',
    element: (
      <MainLayout title={i18n.t('marketplace:purchasedProducts.title', 'Sản phẩm đã mua')}>
        <Suspense fallback={<Loading />}>
          <RouteGuard component={PurchasedProductsPage} type="PROTECT" />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default marketplaceRoutes;
