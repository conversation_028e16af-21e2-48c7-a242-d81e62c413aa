{"data": {"title": "系统数据管理", "description": "集中管理系统数据，包括媒体、知识文件、URL和工具。", "media": {"title": "媒体管理", "description": "集中管理系统中的媒体文件，如图像、视频、音频和文档。", "totalFiles": "文件总数", "manage": "管理媒体", "viewMedia": "媒体详情", "name": "文件名", "size": "大小", "author": "作者", "createdAt": "创建时间", "storageKey": "存储键", "tags": "标签", "selectedItems": "已选择 {{count}} 项", "table": {"name": "文件名", "description": "描述", "size": "大小", "author": "作者", "createdAt": "创建时间", "status": "状态"}, "status": {"ACTIVE": "活动", "PENDING": "待处理", "INACTIVE": "非活动", "DELETED": "已删除"}, "messages": {"deleteSuccess": "媒体删除成功", "deleteError": "删除媒体时发生错误", "batchDeleteSuccess": "成功删除 {{count}} 个媒体", "batchDeleteError": "批量删除时发生错误"}, "confirmDeleteMessage": "您确定要删除此媒体吗？", "confirmBatchDeleteMessage": "您确定要删除选定的 {{count}} 个媒体吗？"}, "knowledgeFiles": {"title": "知识文件管理", "description": "管理用于AI和向量存储的知识文件。", "totalFiles": "文件总数", "manage": "管理知识文件", "viewFile": "查看知识文件", "uploadFiles": "上传知识文件", "selectFiles": "选择文件", "selectFilesToDelete": "请至少选择一个要删除的文件", "supportedFormats": "支持的格式：PDF、DOCX、TXT、CSV、JSON", "fileName": "文件名", "fileSize": "大小", "fileType": "文件类型", "vectorStore": "向量存储", "noVectorStore": "未分配给任何向量存储", "createdAt": "创建时间", "messages": {"deleteSuccess": "知识文件删除成功", "deleteError": "删除知识文件时发生错误", "batchDeleteSuccess": "成功删除 {{count}} 个知识文件", "batchDeleteError": "批量删除时发生错误", "uploadSuccess": "知识文件上传成功", "uploadError": "上传知识文件时发生错误"}, "confirmDeleteMessage": "您确定要删除此知识文件吗？", "confirmBatchDeleteMessage": "您确定要删除选定的 {{count}} 个知识文件吗？"}, "url": {"title": "URL管理", "description": "管理从网站抓取的URL和内容，以供系统使用。", "totalUrls": "URL总数", "manage": "管理URL", "crawl": "抓取URL", "crawlTitle": "抓取URL", "startCrawl": "开始抓取", "crawlDepth": "抓取深度", "maxUrls": "最大URL数", "crawlDepthHelp": "抓取深度，从1开始", "maxUrlsHelp": "最大URL数，0表示无限制", "ignoreRobots": "忽略 robots.txt", "ignoreRobotsHelp": "忽略 robots.txt 文件", "cancel": "取消", "processing": "正在抓取...", "table": {"url": "网址", "title": "标题", "type": "类型", "tags": "标签", "status": "状态", "createdAt": "创建时间"}, "form": {"title": "URL信息", "createTitle": "添加新URL", "editTitle": "编辑URL", "url": "网址", "description": "描述", "type": "类型", "tags": "标签", "status": "状态", "tagsPlaceholder": "输入标签用逗号分隔", "ownedByPlaceholder": "输入所有者名称", "activeStatus": "活动"}, "messages": {"createSuccess": "URL添加成功", "updateSuccess": "URL更新成功", "deleteSuccess": "URL删除成功", "createError": "添加URL失败", "updateError": "更新URL失败", "deleteError": "删除URL失败", "crawlSuccess": "URL抓取成功", "crawlError": "URL抓取失败"}, "confirmDeleteMessage": "您确定要删除此URL吗？"}, "common": {"confirmDelete": "确认删除", "confirmBatchDelete": "确认批量删除", "active": "活动", "inactive": "非活动", "uploading": "上传中...", "upload": "上传", "creating": "创建中...", "create": "创建", "processing": "处理中...", "close": "关闭"}, "vectorStore": {"title": "向量存储管理", "description": "管理用于AI应用和语义搜索的向量存储和嵌入。", "descriptionDetail": "描述描述", "totalStores": "向量存储总数", "manage": "管理向量存储", "viewVectorStore": "查看向量存储", "createVectorStore": "创建新向量存储", "name": "向量存储名称", "namePlaceholder": "输入向量存储名称", "nameRequired": "向量存储名称为必填项", "size": "大小", "files": "文件数量", "agents": "使用的代理数量", "createdAt": "创建时间", "assignFiles": "将文件分配给向量存储", "selectFiles": "选择文件", "assign": "分配文件", "fileSelectionNotImplemented": "文件选择功能尚未实现", "viewDetails": "查看详细信息", "table": {"name": "向量存储名称", "size": "大小", "files": "文件", "agents": "代理", "createdAt": "创建时间"}, "form": {"title": "创建新向量存储", "name": "向量存储名称", "namePlaceholder": "输入向量存储名称"}, "messages": {"createSuccess": "向量存储创建成功", "createError": "创建向量存储时发生错误", "deleteSuccess": "向量存储删除成功", "deleteError": "删除向量存储时发生错误", "assignSuccess": "文件成功分配给向量存储", "assignError": "将文件分配给向量存储时发生错误"}, "confirmDeleteMessage": "您确定要删除此向量存储吗？"}}}