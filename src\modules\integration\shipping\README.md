# Shipping Integration Module

## Overview

The Shipping Integration Module provides comprehensive integration with major Vietnamese shipping providers including GHN (Giao Hàng Nhanh), GHTK (Giao Hàng Tiết Kiệm), and Viettel Post. This module enables rate calculation, order creation, tracking, and management of shipping configurations.

## Features

### Core Features
- **Provider Management**: Add, edit, and configure shipping providers
- **Rate Calculation**: Calculate and compare shipping rates across providers
- **Order Management**: Create, track, and manage shipping orders
- **Connection Testing**: Test API connections with real-time feedback
- **Warehouse Management**: Manage warehouse addresses and configurations

### Supported Providers
- **GHN (Giao Hàng Nhanh)**: Full API integration with rate calculation, order creation, and tracking
- **GHTK (Giao Hàng Tiết Kiệm)**: Complete integration with all major features
- **Viettel Post**: Basic integration with rate calculation and order creation
- **VNPost**: Planned for future implementation

## Architecture

### File Structure
```
src/modules/integration/shipping/
├── components/
│   ├── ShippingProviderList.tsx      # Provider list with status indicators
│   ├── ShippingProviderForm.tsx      # Provider configuration form
│   ├── ShippingTestConnection.tsx    # Connection testing component
│   └── ShippingRateCalculator.tsx    # Rate calculation and comparison
├── types/
│   └── index.ts                      # TypeScript type definitions
├── schemas/
│   └── index.ts                      # Zod validation schemas
├── services/
│   └── index.ts                      # API service functions
├── hooks/
│   └── index.ts                      # React Query hooks
├── constants/
│   └── index.ts                      # Constants and configurations
└── index.ts                          # Module exports
```

### Components

#### ShippingProviderList
- Displays list of configured shipping providers
- Shows status indicators (active/inactive, default)
- Provides quick actions (test, edit, toggle status)
- Responsive grid layout with loading states

#### ShippingProviderForm
- Form for adding/editing shipping providers
- Provider-specific field configurations
- Validation with real-time feedback
- Support for advanced settings (JSON configuration)

#### ShippingTestConnection
- Test API connections with shipping providers
- Real-time feedback and status updates
- Comprehensive test scenarios with sample data
- Error handling and troubleshooting information

#### ShippingRateCalculator
- Calculate shipping rates across all active providers
- Compare rates and highlight best options
- Support for package dimensions and weight
- Export results for analysis

### Data Models

#### ShippingProviderConfiguration
```typescript
interface ShippingProviderConfiguration {
  id: number;
  userId: number;
  providerType: ShippingProviderType;
  providerName: string;
  apiKey: string;
  apiSecret?: string;
  shopId?: string;
  clientId?: string;
  isActive: boolean;
  isDefault: boolean;
  settings?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}
```

#### ShippingRate
```typescript
interface ShippingRate {
  serviceId: string;
  serviceName: string;
  fee: number;
  estimatedDelivery: string;
  currency: string;
  additionalServices?: ShippingAdditionalService[];
}
```

## API Integration

### Service Layer
The module follows a 3-layer architecture:

1. **API Layer**: Raw API calls to shipping providers
2. **Services Layer**: Business logic and data transformation
3. **Hooks Layer**: React Query integration with caching

### Error Handling
- Comprehensive error handling for API failures
- Retry logic for transient errors
- User-friendly error messages
- Fallback mechanisms for provider unavailability

### Caching Strategy
- Rate calculation results cached for 5 minutes
- Provider configurations cached for 10 minutes
- Automatic cache invalidation on updates
- Background refresh for frequently accessed data

## Usage

### Basic Setup

1. **Add a Shipping Provider**:
   ```typescript
   import { ShippingProviderForm } from '@/modules/integration/shipping';
   
   <ShippingProviderForm
     onSuccess={() => console.log('Provider added')}
     onCancel={() => console.log('Cancelled')}
   />
   ```

2. **Display Provider List**:
   ```typescript
   import { ShippingProviderList } from '@/modules/integration/shipping';
   
   <ShippingProviderList
     onCreateNew={() => setShowForm(true)}
     onEdit={(provider) => setEditingProvider(provider)}
   />
   ```

3. **Calculate Rates**:
   ```typescript
   import { useCalculateShippingRates } from '@/modules/integration/shipping';
   
   const { mutate: calculateRates } = useCalculateShippingRates();
   
   calculateRates({
     id: providerId,
     rateRequest: {
       fromAddress: { /* address data */ },
       toAddress: { /* address data */ },
       weight: 1.5,
       dimensions: { length: 20, width: 15, height: 10 }
     }
   });
   ```

### Advanced Configuration

#### Provider-Specific Settings
Each provider supports custom settings through the `settings` field:

```typescript
// GHN Settings
{
  "webhookUrl": "https://your-domain.com/webhook/ghn",
  "enableWebhook": true,
  "defaultServiceType": "express"
}

// GHTK Settings
{
  "pickupType": "auto",
  "deliveryType": "standard",
  "enableInsurance": true
}
```

#### Rate Calculation Options
```typescript
const rateRequest = {
  fromAddress: {
    name: "Sender Name",
    phone: "**********",
    address: "123 Street Name",
    wardName: "Ward 1",
    districtName: "District 1",
    provinceName: "Ho Chi Minh City"
  },
  toAddress: {
    name: "Receiver Name",
    phone: "**********",
    address: "456 Avenue Name",
    wardName: "Ward 2",
    districtName: "District 2",
    provinceName: "Ha Noi"
  },
  weight: 2.0,
  dimensions: {
    length: 30,
    width: 20,
    height: 15
  },
  serviceType: "standard", // optional
  codAmount: 500000, // optional
  insuranceValue: 1000000 // optional
};
```

## Localization

The module supports Vietnamese localization through the integration locale files:

```json
{
  "integration": {
    "shipping": {
      "title": "Quản lý Vận chuyển",
      "description": "Tích hợp với các nhà vận chuyển GHN, GHTK, Viettel Post",
      "addProvider": "Thêm nhà vận chuyển",
      "testConnection": "Test Connection",
      "rateCalculator": "Rate Calculator"
    }
  }
}
```

## Performance Considerations

### Optimization Features
- **Caching**: Intelligent caching of rate calculations and provider data
- **Lazy Loading**: Components loaded on demand
- **Debounced Requests**: Rate calculation requests are debounced
- **Background Sync**: Provider status updates in background

### Best Practices
- Use React Query hooks for data fetching
- Implement proper error boundaries
- Cache rate calculations for repeated requests
- Use loading states for better UX

## Testing

### Unit Tests
- Component rendering and interaction tests
- Service function tests with mocked APIs
- Validation schema tests

### Integration Tests
- API integration tests with mock providers
- End-to-end workflow tests
- Error handling scenario tests

### Manual Testing
- Test connection functionality for each provider
- Rate calculation accuracy verification
- Form validation and error handling

## Troubleshooting

### Common Issues

1. **API Connection Failures**:
   - Verify API credentials are correct
   - Check network connectivity
   - Ensure provider APIs are accessible

2. **Rate Calculation Errors**:
   - Validate address format and completeness
   - Check package dimensions and weight limits
   - Verify provider service availability

3. **Form Validation Issues**:
   - Ensure all required fields are filled
   - Check field format requirements
   - Verify provider-specific field requirements

### Debug Mode
Enable debug logging by setting the environment variable:
```
REACT_APP_SHIPPING_DEBUG=true
```

## Future Enhancements

### Planned Features
- **Bulk Operations**: Bulk rate calculations and order creation
- **Analytics Dashboard**: Shipping cost analysis and reporting
- **Webhook Integration**: Real-time order status updates
- **Additional Providers**: Integration with more shipping providers
- **Mobile Optimization**: Enhanced mobile experience

### API Improvements
- **GraphQL Support**: Alternative to REST APIs
- **Real-time Updates**: WebSocket integration for live updates
- **Batch Processing**: Efficient handling of multiple requests
- **Advanced Caching**: Redis-based caching for better performance

## Contributing

When contributing to the Shipping Integration Module:

1. Follow the established file structure
2. Add proper TypeScript types for new features
3. Include validation schemas for new data structures
4. Write comprehensive tests for new functionality
5. Update documentation for API changes
6. Follow the existing code style and patterns

## Support

For issues and questions related to the Shipping Integration Module:

1. Check the troubleshooting section
2. Review the API documentation
3. Test with the connection testing component
4. Contact the development team for provider-specific issues
