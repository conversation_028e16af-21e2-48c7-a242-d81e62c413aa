import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CartService, OrderService, PaymentService } from '../services';
import { AddToCartDto, ApiCart, UpdateCartItemDto } from '../services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '@/modules/marketplace';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Hook để quản lý giỏ hàng với real API
 */

/**
 * Hook để lấy giỏ hàng hiện tại
 */
export const useCart = () => {
  return useQuery({
    queryKey: PRODUCT_QUERY_KEYS.CART,
    queryFn: () => CartService.getCart(),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để thêm sản phẩm vào giỏ hàng
 */
export const useAddToCart = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddToCartDto) => CartService.addToCart(data),
    onSuccess: (updatedCart) => {
      // Cập nhật cache giỏ hàng
      queryClient.setQueryData(PRODUCT_QUERY_KEYS.CART, updatedCart);

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Đã thêm sản phẩm vào giỏ hàng' });
    },
    onError: (error: unknown) => {
      // Hiển thị thông báo lỗi
      const errorMessage = (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Không thể thêm sản phẩm vào giỏ hàng';
      NotificationUtil.error({ message: errorMessage });
    },
  });
};

/**
 * Hook để cập nhật số lượng sản phẩm trong giỏ hàng
 */
export const useUpdateCartItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ cartItemId, data }: { cartItemId: number; data: UpdateCartItemDto }) =>
      CartService.updateCartItem(cartItemId, data),
    onSuccess: (updatedCart) => {
      // Cập nhật cache giỏ hàng
      queryClient.setQueryData(PRODUCT_QUERY_KEYS.CART, updatedCart);

      // Không hiển thị thông báo cho việc cập nhật quantity để tránh spam
      // NotificationUtil.success({ message: 'Đã cập nhật giỏ hàng' });
    },
    onError: (error: unknown) => {
      // Hiển thị thông báo lỗi
      const errorMessage = (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Không thể cập nhật giỏ hàng';
      NotificationUtil.error({ message: errorMessage });
    },
  });
};

/**
 * Hook để xóa sản phẩm khỏi giỏ hàng
 */
export const useRemoveCartItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (cartItemId: number) => CartService.removeCartItem(cartItemId),
    onSuccess: (updatedCart) => {
      // Cập nhật cache giỏ hàng
      queryClient.setQueryData(PRODUCT_QUERY_KEYS.CART, updatedCart);

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Đã xóa sản phẩm khỏi giỏ hàng' });
    },
    onError: (error: unknown) => {
      // Hiển thị thông báo lỗi
      const errorMessage = (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Không thể xóa sản phẩm khỏi giỏ hàng';
      NotificationUtil.error({ message: errorMessage });
    },
  });
};

/**
 * Hook để thanh toán
 */
export const useProcessPayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productIds: number[]) =>
      PaymentService.processPayment({ productIds }),
    onSuccess: (paymentResult) => {
      // Làm mới giỏ hàng sau khi thanh toán thành công
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.CART });

      // Làm mới lịch sử mua hàng
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.PURCHASE_HISTORY });

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: `Thanh toán thành công! Đã sử dụng ${paymentResult.totalPoint} R-Point. Số dư còn lại: ${paymentResult.remainingBalance} R-Point`
      });

      return paymentResult;
    },
    onError: (error: unknown) => {
      // Hiển thị thông báo lỗi
      const errorMessage = (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Thanh toán thất bại';
      NotificationUtil.error({ message: errorMessage });
    },
  });
};

/**
 * Hook để lấy lịch sử mua hàng
 */
export const usePurchaseHistory = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: [...PRODUCT_QUERY_KEYS.PURCHASE_HISTORY, params],
    queryFn: () => OrderService.getPurchaseHistory(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Utility functions để làm việc với giỏ hàng
 */
export const cartUtils = {
  /**
   * Tính tổng giá trị giỏ hàng
   */
  calculateTotal: (cart: ApiCart | undefined): number => {
    if (!cart || !cart.items) return 0;
    return cart.totalValue;
  },

  /**
   * Tính tổng số lượng sản phẩm trong giỏ hàng
   */
  calculateTotalItems: (cart: ApiCart | undefined): number => {
    if (!cart || !cart.items) return 0;
    return cart.items.reduce((total, item) => total + item.quantity, 0);
  },

  /**
   * Kiểm tra sản phẩm có trong giỏ hàng không
   */
  isProductInCart: (cart: ApiCart | undefined, productId: number): boolean => {
    if (!cart || !cart.items) return false;
    return cart.items.some(item => item.productId === productId);
  },

  /**
   * Lấy số lượng của sản phẩm trong giỏ hàng
   */
  getProductQuantity: (cart: ApiCart | undefined, productId: number): number => {
    if (!cart || !cart.items) return 0;
    const item = cart.items.find(item => item.productId === productId);
    return item ? item.quantity : 0;
  },

  /**
   * Lấy cart item theo product ID
   */
  getCartItemByProductId: (cart: ApiCart | undefined, productId: number) => {
    if (!cart || !cart.items) return null;
    return cart.items.find(item => item.productId === productId) || null;
  },
};

/**
 * Hook tổng hợp để quản lý giỏ hàng
 */
export const useCartManager = () => {
  const cartQuery = useCart();
  const addToCartMutation = useAddToCart();
  const updateCartItemMutation = useUpdateCartItem();
  const removeCartItemMutation = useRemoveCartItem();
  const processPaymentMutation = useProcessPayment();

  return {
    // Data
    cart: cartQuery.data,
    isLoading: cartQuery.isLoading,
    error: cartQuery.error,

    // Actions
    addToCart: addToCartMutation.mutate,
    updateCartItem: updateCartItemMutation.mutate,
    removeCartItem: removeCartItemMutation.mutate,
    processPayment: processPaymentMutation.mutate,

    // Loading states
    isAddingToCart: addToCartMutation.isPending,
    isUpdatingCart: updateCartItemMutation.isPending,
    isRemovingFromCart: removeCartItemMutation.isPending,
    isProcessingPayment: processPaymentMutation.isPending,

    // Utilities
    ...cartUtils,

    // Refetch
    refetch: cartQuery.refetch,
  };
};
