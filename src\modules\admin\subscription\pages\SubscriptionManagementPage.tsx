import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

interface SubscriptionManagementPageProps {
  initialFilter?: string;
}

/**
 * Trang tổng quan quản lý Subscription
 */
const SubscriptionManagementPage: React.FC<SubscriptionManagementPageProps> = () => {
  const { t } = useTranslation(['admin', 'common']);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Subscription Plans Card */}
        <ModuleCard
          title={t('admin:subscription.management.cards.plans.title')}
          description={t('admin:subscription.management.cards.plans.description')}
          icon="package"
          linkTo="/admin/subscription/plans"
        />

        {/* Plan Pricing Card */}
        <ModuleCard
          title={t('admin:subscription.management.cards.pricing.title')}
          description={t('admin:subscription.management.cards.pricing.description')}
          icon="credit-card"
          linkTo="/admin/subscription/plan-pricing"
        />

        {/* Subscriptions Card */}
        <ModuleCard
          title={t('admin:subscription.management.cards.subscriptions.title')}
          description={t('admin:subscription.management.cards.subscriptions.description')}
          icon="users"
          linkTo="/admin/subscription/subscriptions"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default SubscriptionManagementPage;
