import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ConfirmDeleteModal } from '@/shared/components/common';
import { SortOrder, TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { ModernMenuTrigger } from '@/shared/components/common/ModernMenu';

import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

import {
  useShippingProviderConfigurations,
  useCreateShippingProviderConfiguration,
  useUpdateShippingProviderConfiguration,
  useDeleteShippingProviderConfiguration,
  useTestShippingProviderConfiguration,
  useSetShippingProviderAsDefault,
} from '../shipping/hooks';
import {
  ShippingProviderConfiguration,
  ShippingProviderQueryParams,
  CreateShippingProviderDto,
  UpdateShippingProviderDto,
  ShippingProviderType,
} from '../shipping/types';

import ShippingProviderForm from '../shipping/components/ShippingProviderForm';

/**
 * Trang quản lý tích hợp vận chuyển
 */
const ShippingIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [shippingProviders, setShippingProviders] = useState<ShippingProviderConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalItems, setTotalItems] = useState(0);
  const [providerToDelete, setProviderToDelete] = useState<ShippingProviderConfiguration | null>(null);
  const [providerToEdit, setProviderToEdit] = useState<ShippingProviderConfiguration | null>(null);
  const [providerToView, setProviderToView] = useState<ShippingProviderConfiguration | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sử dụng hook animation cho form tạo mới
  const { isVisible: isCreateFormVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const { isVisible: isEditFormVisible, showForm: showEditForm, hideForm: hideEditForm } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết
  const { isVisible: isViewFormVisible, showForm: showViewForm, hideForm: hideViewForm } = useSlideForm();

  // Xử lý hiển thị popup xác nhận xóa
  const handleShowDeleteConfirm = useCallback((provider: ShippingProviderConfiguration) => {
    setProviderToDelete(provider);
    setShowDeleteConfirm(true);
  }, []);

  // Xử lý hiển thị form chỉnh sửa
  const handleShowEditForm = useCallback((provider: ShippingProviderConfiguration) => {
    setProviderToEdit(provider);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hiển thị form xem chi tiết
  const handleShowViewForm = useCallback((provider: ShippingProviderConfiguration) => {
    setProviderToView(provider);
    showViewForm();
  }, [showViewForm]);

  // Xử lý test connection
  const testProviderMutation = useTestShippingProviderConfiguration();
  const handleTestProvider = useCallback(async (provider: ShippingProviderConfiguration) => {
    try {
      await testProviderMutation.mutateAsync({ id: provider.id });
    } catch (error) {
      console.error('Error testing provider:', error);
    }
  }, [testProviderMutation]);

  // Xử lý set as default
  const setDefaultMutation = useSetShippingProviderAsDefault();
  const handleSetAsDefault = useCallback(async (provider: ShippingProviderConfiguration) => {
    try {
      await setDefaultMutation.mutateAsync(provider.id);
    } catch (error) {
      console.error('Error setting default provider:', error);
    }
  }, [setDefaultMutation]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo<TableColumn<ShippingProviderConfiguration>[]>(() => {
    const allColumns: TableColumn<ShippingProviderConfiguration>[] = [
      {
        key: 'providerName',
        title: t('admin:integration.shipping.list.columns.providerName'),
        dataIndex: 'providerName',
        width: '20%',
        sortable: true,
      },
      {
        key: 'providerType',
        title: t('admin:integration.shipping.list.columns.providerType'),
        dataIndex: 'providerType',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          const type = value as string;
          const typeLabels: Record<string, string> = {
            'ghn': 'Giao Hàng Nhanh',
            'ghtk': 'Giao Hàng Tiết Kiệm',
            'viettel-post': 'Viettel Post',
            'vnpost': 'VN Post',
          };
          return typeLabels[type] || type;
        },
      },
      {
        key: 'shopId',
        title: t('admin:integration.shipping.list.columns.shopId'),
        dataIndex: 'shopId',
        width: '15%',
        sortable: true,
      },
      {
        key: 'isDefault',
        title: t('admin:integration.shipping.list.columns.default'),
        dataIndex: 'isDefault',
        width: '10%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
          }`}>
            {value ? t('common:default') : ''}
          </span>
        ),
      },
      {
        key: 'isActive',
        title: t('admin:integration.shipping.list.columns.status'),
        dataIndex: 'isActive',
        width: '10%',
        render: (value: unknown) => (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {value ? t('common:active') : t('common:inactive')}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('admin:integration.shipping.list.columns.actions'),
        render: (_: unknown, record: ShippingProviderConfiguration) => {
          const menuItems = [
            {
              label: t('admin:integration.shipping.actions.edit'),
              icon: 'edit',
              onClick: () => handleShowEditForm(record),
            },
            {
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleShowViewForm(record),
            },
            {
              label: t('admin:integration.shipping.actions.test'),
              icon: 'play',
              onClick: () => handleTestProvider(record),
            },
            ...(!record.isDefault ? [{
              label: t('admin:integration.shipping.actions.setDefault'),
              icon: 'star',
              onClick: () => handleSetAsDefault(record),
            }] : []),
            {
              label: t('admin:integration.shipping.actions.delete'),
              icon: 'trash',
              onClick: () => handleShowDeleteConfirm(record),
              variant: 'primary' as const,
            },
          ];

          return <ModernMenuTrigger items={menuItems} placement="left" />;
        },
      },
    ];

    return allColumns;
  }, [t, handleShowDeleteConfirm, handleShowEditForm, handleShowViewForm, handleTestProvider, handleSetAsDefault]);

  // Sử dụng hook useDataTable để quản lý dữ liệu bảng
  const dataTableConfig = useDataTableConfig<ShippingProviderConfiguration, ShippingProviderQueryParams>({
    columns,
    filterOptions: [
      {
        id: 'all',
        label: t('common:all'),
        icon: 'list',
        value: 'all',
      },
      {
        id: 'ghn',
        label: 'Giao Hàng Nhanh',
        icon: 'truck',
        value: 'ghn',
      },
      {
        id: 'ghtk',
        label: 'Giao Hàng Tiết Kiệm',
        icon: 'truck',
        value: 'ghtk',
      },
      {
        id: 'viettel-post',
        label: 'Viettel Post',
        icon: 'truck',
        value: 'viettel-post',
      },
      {
        id: 'vnpost',
        label: 'VN Post',
        icon: 'truck',
        value: 'vnpost',
      },
      {
        id: 'active',
        label: t('common:active'),
        icon: 'check',
        value: true,
      },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'x',
        value: false,
      },
    ],
    showDateFilter: false,
    createQueryParams: params => ({
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      providerType: params.filterValue !== 'all' && typeof params.filterValue === 'string' ? params.filterValue as ShippingProviderType : undefined,
      isActive: typeof params.filterValue === 'boolean' ? params.filterValue : undefined,
    }),
  });

  const dataTable = useDataTable(dataTableConfig);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Tạo query params cho API
  const queryParams = useMemo<ShippingProviderQueryParams>(() => {
    const params: ShippingProviderQueryParams = {
      page: dataTable.tableData.currentPage,
      limit: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm || undefined,
    };

    // Thêm filter params dựa trên selectedValue
    const filterValue = dataTable.filter.selectedValue;
    if (filterValue !== 'all') {
      if (typeof filterValue === 'string') {
        params.providerType = filterValue as ShippingProviderType;
      } else if (typeof filterValue === 'boolean') {
        params.isActive = filterValue;
      }
    }

    return params;
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.filter.selectedValue,
  ]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      'ghn': 'Giao Hàng Nhanh',
      'ghtk': 'Giao Hàng Tiết Kiệm',
      'viettel-post': 'Viettel Post',
      'vnpost': 'VN Post',
      'true': t('common:active'),
      'false': t('common:inactive'),
    },
    t,
  });

  // Hooks để gọi API
  const {
    data: shippingProvidersData,
    isLoading: isLoadingProviders,
    error: providersError,
  } = useShippingProviderConfigurations(queryParams);

  const createProviderMutation = useCreateShippingProviderConfiguration();
  const updateProviderMutation = useUpdateShippingProviderConfiguration();
  const deleteProviderMutation = useDeleteShippingProviderConfiguration();

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (shippingProvidersData?.result) {
      // API mới luôn trả về cấu trúc PaginatedResult
      setShippingProviders(shippingProvidersData.result.items || []);
      setTotalItems(shippingProvidersData.result.meta?.totalItems || 0);
    }

    setIsLoading(isLoadingProviders);
  }, [shippingProvidersData, providersError, isLoadingProviders]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      dataTable.tableData.handlePageChange(page, newPageSize);
    },
    [dataTable.tableData]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback(
    (term: string) => {
      dataTable.tableData.handleSearch(term);
    },
    [dataTable.tableData]
  );

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback(
    (column: string | null, order: SortOrder) => {
      dataTable.tableData.handleSortChange(column, order);
    },
    [dataTable.tableData]
  );

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setProviderToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!providerToDelete) return;

    try {
      await deleteProviderMutation.mutateAsync(providerToDelete.id);
      setShowDeleteConfirm(false);
      setProviderToDelete(null);
    } catch (error) {
      console.error('Error deleting shipping provider:', error);
    }
  }, [providerToDelete, deleteProviderMutation]);

  // Xử lý submit form tạo mới
  const handleSubmitCreateProvider = useCallback(
    async (values: Record<string, unknown>) => {
      try {
        setIsSubmitting(true);
        const createData: CreateShippingProviderDto = values as unknown as CreateShippingProviderDto;

        await createProviderMutation.mutateAsync(createData);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating shipping provider:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [createProviderMutation, hideCreateForm]
  );

  // Xử lý submit form chỉnh sửa
  const handleSubmitEditProvider = useCallback(
    async (values: Record<string, unknown>) => {
      if (!providerToEdit) return;

      try {
        setIsSubmitting(true);
        const updateData: UpdateShippingProviderDto = values as unknown as UpdateShippingProviderDto;

        await updateProviderMutation.mutateAsync({
          id: providerToEdit.id,
          data: updateData,
        });
        hideEditForm();
        setProviderToEdit(null);
      } catch (error) {
        console.error('Error updating shipping provider:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [providerToEdit, updateProviderMutation, hideEditForm]
  );

  // Xử lý đóng form chỉnh sửa
  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setTimeout(() => {
      setProviderToEdit(null);
    }, 300);
  }, [hideEditForm]);

  // Xử lý đóng form xem chi tiết
  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setTimeout(() => {
      setProviderToView(null);
    }, 300);
  }, [hideViewForm]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo<TableColumn<ShippingProviderConfiguration>[]>(() => {
    if (visibleColumns.length === 0) {
      setVisibleColumns([
        { id: 'all', label: t('common:all'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <div className="space-y-4">
        <div>
          {/* MenuIconBar */}
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => showCreateForm()}
            items={[
              {
                id: 'all',
                label: t('common:all'),
                icon: 'list',
                onClick: () => '',
              },
            ]}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            showDateFilter={false}
            showColumnFilter={true}
          />

          {/* ActiveFilters */}
          <ActiveFilters
            searchTerm={dataTable.tableData.searchTerm}
            onClearSearch={handleClearSearch}
            sortBy={dataTable.tableData.sortBy}
            sortDirection={dataTable.tableData.sortDirection as SortDirection}
            onClearSort={handleClearSort}
            onClearAll={handleClearAll}
          />
        </div>

        {/* SlideInForm cho form tạo mới */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <ShippingProviderForm
            onSubmit={handleSubmitCreateProvider}
            onCancel={hideCreateForm}
            isSubmitting={isSubmitting}
          />
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {providerToEdit && (
            <ShippingProviderForm
              initialData={providerToEdit}
              onSubmit={handleSubmitEditProvider}
              onCancel={handleCloseEditForm}
              isSubmitting={isSubmitting}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {providerToView && (
            <ShippingProviderForm
              initialData={providerToView}
              onSubmit={() => {}}
              onCancel={handleCloseViewForm}
              readOnly={true}
            />
          )}
        </SlideInForm>

        <Card className="overflow-hidden">
          <Table<ShippingProviderConfiguration>
            columns={filteredColumns}
            data={shippingProviders}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:integration.shipping.confirmations.deleteTitle')}
        message={t('admin:integration.shipping.confirmations.delete')}
        itemName={providerToDelete?.providerName}
      />
    </div>
  );
};

export default ShippingIntegrationPage;
