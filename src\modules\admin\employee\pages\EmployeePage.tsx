/**
 * Trang chính module quản lý nhân viên
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import { ModuleCard } from '@/modules/components/card';

/**
 * Trang chính module quản lý nhân viên
 */
const EmployeePage: React.FC = () => {
  const { t } = useTranslation(['employee', 'common']);

  // Danh sách các card chức năng
  const employeeModules = [
    {
      title: t('employee.list.title', 'Danh sách nhân viên'),
      description: t('employee.list.description', 'Quản lý danh sách nhân viên trong hệ thống'),
      icon: 'users',
      linkTo: '/admin/employees/list',
      linkText: t('common:manage', 'Quản lý'),
      count: '0',
      countLabel: t('employee.list.totalEmployees', '<PERSON><PERSON><PERSON> số nhân viên'),
    },
    {
      title: t('employee.role.title', '<PERSON><PERSON> quyền'),
      description: t('employee.role.description', '<PERSON>uản lý vai trò và phân quyền cho nhân viên'),
      icon: 'lock',
      linkTo: '/admin/employees/roles',
      linkText: t('common:manage', 'Quản lý'),
      count: '0',
      countLabel: t('employee.role.totalRoles', 'Tổng số vai trò'),
    },
    {
      title: t('employee.permission.title', 'Quyền hệ thống'),
      description: t('employee.permission.description', 'Quản lý các quyền trong hệ thống'),
      icon: 'settings',
      linkTo: '/admin/employees/permissions',
      linkText: t('common:manage', 'Quản lý'),
      count: '0',
      countLabel: t('employee.permission.totalPermissions', 'Tổng số quyền'),
    },
  ];

  return (
    <div>
      <ResponsiveGrid
        gap={6}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
      >
        {employeeModules.map((module, index) => (
          <ModuleCard
            key={index}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
          />
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default EmployeePage;
