# AddProductForm Upload Logic Test

## Mô tả
File này mô tả logic upload đã đư<PERSON><PERSON> cải thiện trong `AddProductForm.tsx` để đảm bảo việc PUT file lên S3 hoạt động đúng.

## C<PERSON><PERSON> cải thiện đã thực hiện

### 1. C<PERSON>i thiện logging
- Thêm prefix `[SUBMIT]`, `[IMAGES]`, `[USER_MANUAL]`, `[DETAIL]`, `[UPLOAD]` để dễ theo dõi
- Truncate URL trong log để tránh log quá dài
- Thêm thông tin về key và URL CDN sau khi upload thành công

### 2. C<PERSON>i thiện error handling
- Thêm try-catch trong các hàm upload
- Truncate response body trong error log
- Kiểm tra response structure trước khi xử lý

### 3. Logic upload đượ<PERSON> cải thiện
- Kiểm tra response structure trướ<PERSON> khi upload
- Upload song song tất cả files
- <PERSON><PERSON> chi tiết về từng bước upload
- <PERSON><PERSON><PERSON> thị URL CDN sau khi upload thành công

## Flow hoạt động

1. **Submit form** → Tạo sản phẩm qua API
2. **Nhận response** → Chứa uploadUrls với presigned URLs
3. **Upload files song song**:
   - Images: PUT lên `imagesUploadUrls[].url`
   - User Manual: PUT lên `userManualUploadUrl.url`
   - Detail: PUT lên `detailUploadUrl.url`
4. **Sau upload thành công** → Files có thể truy cập qua `https://cdn.redai.vn/{key}`

## Response structure mong đợi

```json
{
  "code": 201,
  "message": "Created Successfully",
  "result": {
    "id": "178",
    "uploadUrls": {
      "productId": "178",
      "imagesUploadUrls": [
        {
          "url": "https://redaivn.hn.ss.bfcplatform.vn/...",
          "key": "marketplace/IMAGE/2025/05/...",
          "index": 0
        }
      ],
      "userManualUploadUrl": {
        "url": "https://redaivn.hn.ss.bfcplatform.vn/...",
        "key": "marketplace/DOCUMENT/2025/05/...",
        "expiresAt": 1748661445908
      },
      "detailUploadUrl": {
        "url": "https://redaivn.hn.ss.bfcplatform.vn/...",
        "key": "marketplace/DOCUMENT/2025/05/...",
        "expiresAt": 1748661445914
      }
    }
  }
}
```

## Test cases

### Test 1: Upload ảnh thành công
- Chọn 1-2 ảnh JPG/PNG
- Submit form
- Kiểm tra log: `✅ [IMAGES] Image X uploaded successfully`
- Kiểm tra log: `✅ [IMAGES] Image X will be available at: https://cdn.redai.vn/{key}`

### Test 2: Upload user manual thành công
- Chọn file PDF/DOC
- Submit form
- Kiểm tra log: `✅ [USER_MANUAL] User manual uploaded successfully`
- Kiểm tra log: `✅ [USER_MANUAL] User manual will be available at: https://cdn.redai.vn/{key}`

### Test 3: Upload detail thành công
- Chọn file PDF/DOC
- Submit form
- Kiểm tra log: `✅ [DETAIL] Detail file uploaded successfully`
- Kiểm tra log: `✅ [DETAIL] Detail file will be available at: https://cdn.redai.vn/{key}`

### Test 4: Upload tất cả files
- Chọn ảnh + user manual + detail
- Submit form
- Kiểm tra log: `🎉 [UPLOAD] All uploads completed successfully`

## Debug tips

1. Mở Developer Tools → Console
2. Tìm log với prefix `[SUBMIT]`, `[IMAGES]`, `[USER_MANUAL]`, `[DETAIL]`
3. Kiểm tra response structure trong log `🔍 [SUBMIT] Upload URLs received:`
4. Kiểm tra từng bước upload trong log
5. Nếu có lỗi, xem log `❌` để biết nguyên nhân

## Lưu ý quan trọng

- Chỉ upload file đầu tiên cho user manual và detail (vì API chỉ trả về 1 URL)
- Images có thể upload nhiều file (theo số lượng URLs trong array)
- Files sẽ có thể truy cập qua `https://cdn.redai.vn/{key}` sau khi upload thành công
- Nếu không có files để upload, form vẫn hoạt động bình thường
