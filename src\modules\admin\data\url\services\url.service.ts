import { apiClient } from '@/shared/api';
import {
  Url,
  UrlListResponse,
  UrlSearchParams,
  CreateUrlParams,
  UpdateUrlParams,
  CrawlUrlParams,
  CrawlUrlResult,
} from '../types/url.types';

// Đường dẫn API chung cho admin URL
const ADMIN_URL_API_PATH = '/data/admin/url';

/**
 * Service để tương tác với API URL của admin
 */
export const UrlService = {
  /**
   * Lấy danh sách URL với phân trang và tìm kiếm
   * @param params Tham số tìm kiếm
   * @returns Danh sách URL đã phân trang
   */
  async getUrls(params: UrlSearchParams = {}): Promise<UrlListResponse> {
    const response = await apiClient.get<UrlListResponse>(ADMIN_URL_API_PATH, {
      params,
    });
    return response.result;
  },

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết của một URL
   * @param id ID của URL
   * @returns Thông tin chi tiết URL
   */
  async getUrlById(id: string): Promise<Url> {
    const response = await apiClient.get<Url>(`${ADMIN_URL_API_PATH}/${id}`);
    return response.result;
  },

  /**
   * Tìm kiếm URL theo từ khóa
   * @param keyword Từ khóa tìm kiếm
   * @param limit Số lượng kết quả tối đa
   * @returns Danh sách URL phù hợp
   */
  async searchUrls(keyword: string, limit: number = 10): Promise<Url[]> {
    const response = await apiClient.get<Url[]>(`${ADMIN_URL_API_PATH}/search`, {
      params: { keyword, limit },
    });
    return response.result;
  },

  /**
   * Tạo URL mới
   * @param urlData Dữ liệu URL mới
   * @returns URL đã tạo
   */
  async createUrl(urlData: CreateUrlParams): Promise<Url> {
    const response = await apiClient.post<Url>(ADMIN_URL_API_PATH, urlData);
    return response.result;
  },

  /**
   * Cập nhật URL
   * @param id ID của URL
   * @param urlData Dữ liệu cập nhật
   * @returns URL đã cập nhật
   */
  async updateUrl(id: string, urlData: UpdateUrlParams): Promise<Url> {
    const response = await apiClient.put<Url>(`${ADMIN_URL_API_PATH}/${id}`, urlData);
    return response.result;
  },

  async deleteUrl(ids: string[]): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `${ADMIN_URL_API_PATH}/batch`,
      {
        data: { ids }, // Truyền body chứa mảng ids
      }
    );
    return response.result;
  },

  /**
   * Đảo ngược trạng thái kích hoạt của URL
   * @param id ID của URL
   * @returns URL đã cập nhật
   */
  async toggleUrlStatus(id: string): Promise<Url> {
    const response = await apiClient.post<Url>(`${ADMIN_URL_API_PATH}/${id}/toggle`);
    return response.result;
  },

  /**
   * Crawl URL và các URL con
   * @param params Tham số crawl
   * @returns Kết quả crawl
   */
  async crawlUrl(params: CrawlUrlParams): Promise<CrawlUrlResult> {
    const response = await apiClient.post<CrawlUrlResult>(`${ADMIN_URL_API_PATH}/crawl`, params);
    return response.result;
  },
};
