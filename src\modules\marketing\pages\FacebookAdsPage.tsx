import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Icon,
  ResponsiveGrid,
  EmptyState,
  Loading,
} from '@/shared/components/common';
import { useFacebookAuth } from '../hooks/facebook-ads/useFacebookAuth';
import { useRecentFacebookAdsCampaigns } from '../hooks/facebook-ads/useFacebookAdsCampaigns';
import FacebookAuthButton from '../components/facebook-ads/FacebookAuthButton';
import FacebookAccountManager from '../components/facebook-ads/FacebookAccountManager';
import FacebookCampaignManager from '../components/facebook-ads/FacebookCampaignManager';
import FacebookOverviewCards from '../components/facebook-ads/FacebookOverviewCards';
import FacebookQuickStats from '../components/facebook-ads/FacebookQuickStats';
import FacebookPerformanceDashboard from '../components/facebook-ads/FacebookPerformanceDashboard';
import FacebookCampaignMonitor from '../components/facebook-ads/FacebookCampaignMonitor';
import FacebookBudgetManager from '../components/facebook-ads/FacebookBudgetManager';
import FacebookNotificationCenter from '../components/facebook-ads/FacebookNotificationCenter';

/**
 * Trang tích hợp & quản lý Facebook Ads
 */
const FacebookAdsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'overview' | 'accounts' | 'campaigns' | 'analytics' | 'budget' | 'monitor'>('overview');

  const {
    isAuthenticated,
    user,
    adAccounts,
    isLoading: authLoading,
  } = useFacebookAuth();

  const {
    data: recentCampaigns,
    isLoading: campaignsLoading,
  } = useRecentFacebookAdsCampaigns(5);

  // Navigation handlers
  const handleNavigateToAccounts = () => {
    navigate('/marketing/facebook-ads/accounts');
  };

  const handleNavigateToCampaigns = () => {
    navigate('/marketing/facebook-ads/campaigns');
  };

  const handleCreateCampaign = () => {
    navigate('/marketing/facebook-ads/campaigns/create');
  };

  const handleViewCampaign = (campaignId: string) => {
    navigate(`/marketing/facebook-ads/campaigns/${campaignId}`);
  };

  // Loading state
  if (authLoading) {
    return (
      <div className="w-full bg-background text-foreground">
        <Loading size="lg" className="flex justify-center py-12" />
      </div>
    );
  }

  // Not authenticated state
  if (!isAuthenticated) {
    return (
      <div className="w-full bg-background text-foreground">
        <Card className="p-8 text-center">
          <EmptyState
            icon="facebook"
            title={t('marketing:facebookAds.notConnected.title', 'Chưa kết nối Facebook')}
            description={t('marketing:facebookAds.notConnected.description', 'Kết nối tài khoản Facebook để bắt đầu quản lý quảng cáo')}
            actions={
              <FacebookAuthButton
                variant="primary"
                size="lg"
                onAuthSuccess={() => {
                  console.log('Facebook connected successfully');
                }}
              />
            }
          />
        </Card>
      </div>
    );
  }



  // Render quick actions
  const renderQuickActions = () => (
    <Card className="p-4">
      <Typography variant="h6" className="mb-4">
        {t('marketing:facebookAds.quickActions.title', 'Hành động nhanh')}
      </Typography>
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
        <Button
          variant="outline"
          className="h-auto p-4 flex flex-col items-center space-y-2"
          onClick={handleNavigateToAccounts}
        >
          <Icon name="facebook" size="lg" className="text-blue-600" />
          <Typography variant="body2" className="text-center">
            {t('marketing:facebookAds.quickActions.manageAccounts', 'Quản lý tài khoản')}
          </Typography>
        </Button>

        <Button
          variant="outline"
          className="h-auto p-4 flex flex-col items-center space-y-2"
          onClick={handleNavigateToCampaigns}
        >
          <Icon name="campaign" size="lg" className="text-orange-600" />
          <Typography variant="body2" className="text-center">
            {t('marketing:facebookAds.quickActions.manageCampaigns', 'Quản lý chiến dịch')}
          </Typography>
        </Button>

        <Button
          variant="outline"
          className="h-auto p-4 flex flex-col items-center space-y-2"
          onClick={handleCreateCampaign}
        >
          <Icon name="plus" size="lg" className="text-green-600" />
          <Typography variant="body2" className="text-center">
            {t('marketing:facebookAds.quickActions.createCampaign', 'Tạo chiến dịch')}
          </Typography>
        </Button>

        <Button
          variant="outline"
          className="h-auto p-4 flex flex-col items-center space-y-2"
          onClick={() => navigate('/marketing/facebook-ads/insights')}
        >
          <Icon name="chart-bar" size="lg" className="text-purple-600" />
          <Typography variant="body2" className="text-center">
            {t('marketing:facebookAds.quickActions.viewInsights', 'Xem báo cáo')}
          </Typography>
        </Button>
      </ResponsiveGrid>
    </Card>
  );

  // Render recent campaigns
  const renderRecentCampaigns = () => {
    if (campaignsLoading) {
      return (
        <Card className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('marketing:facebookAds.recentCampaigns.title', 'Chiến dịch gần đây')}
          </Typography>
          <Loading size="md" className="flex justify-center py-8" />
        </Card>
      );
    }

    const campaigns = recentCampaigns?.result?.items || [];

    return (
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Typography variant="h6">
            {t('marketing:facebookAds.recentCampaigns.title', 'Chiến dịch gần đây')}
          </Typography>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNavigateToCampaigns}
          >
            {t('marketing:facebookAds.recentCampaigns.viewAll', 'Xem tất cả')}
          </Button>
        </div>

        {campaigns.length === 0 ? (
          <EmptyState
            icon="campaign"
            title={t('marketing:facebookAds.recentCampaigns.empty.title', 'Chưa có chiến dịch nào')}
            description={t('marketing:facebookAds.recentCampaigns.empty.description', 'Tạo chiến dịch đầu tiên để bắt đầu quảng cáo')}
            actions={
              <Button
                variant="primary"
                onClick={handleCreateCampaign}
              >
                <Icon name="plus" className="mr-2" />
                {t('marketing:facebookAds.recentCampaigns.empty.createFirst', 'Tạo chiến dịch đầu tiên')}
              </Button>
            }
          />
        ) : (
          <div className="space-y-3">
            {campaigns.slice(0, 3).map((campaign) => (
              <div
                key={campaign.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                onClick={() => handleViewCampaign(campaign.id.toString())}
              >
                <div className="flex items-center space-x-3">
                  <Icon name="campaign" className="text-orange-600" />
                  <div>
                    <Typography variant="body2" className="font-medium">
                      {campaign.name}
                    </Typography>
                    <Typography variant="caption" className="text-muted-foreground">
                      {campaign.objective} • {campaign.status}
                    </Typography>
                  </div>
                </div>
                <Icon name="chevron-right" size="sm" className="text-muted-foreground" />
              </div>
            ))}
          </div>
        )}
      </Card>
    );
  };

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'accounts':
        return (
          <FacebookAccountManager
            showFilters={true}
            showAddButton={true}
            onSyncAccount={(accountId) => console.log('Sync account:', accountId)}
            onEditAccount={(accountId) => console.log('Edit account:', accountId)}
            onDeleteAccount={(accountId) => console.log('Delete account:', accountId)}
          />
        );
      case 'campaigns':
        return (
          <FacebookCampaignManager
            showFilters={true}
            showCreateButton={true}
            onViewCampaign={handleViewCampaign}
            onEditCampaign={(campaignId) => console.log('Edit campaign:', campaignId)}
            onCreateCampaign={handleCreateCampaign}
          />
        );
      case 'analytics':
        return (
          <div className="space-y-6">
            <FacebookPerformanceDashboard />
          </div>
        );
      case 'budget':
        return (
          <FacebookBudgetManager
            onBudgetUpdate={(itemId, newBudget) => {
              console.log('Update budget:', itemId, newBudget);
            }}
          />
        );
      case 'monitor':
        return (
          <FacebookCampaignMonitor
            refreshInterval={30}
          />
        );
      default:
        return (
          <div className="space-y-6">
            <FacebookOverviewCards />
            {renderQuickActions()}
            {renderRecentCampaigns()}
          </div>
        );
    }
  };

  // Main render
  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <Card className="p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Icon name="facebook" size="xl" className="text-blue-600" />
            <div>
              <Typography variant="h4" className="font-bold">
                {t('marketing:facebookAds.title', 'Facebook Ads')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground mb-2">
                {t('marketing:facebookAds.description', 'Tích hợp và quản lý chiến dịch Facebook Ads')}
              </Typography>
              <FacebookQuickStats />
            </div>
          </div>

          {/* User info and auth button */}
          <div className="flex items-center space-x-4">
            {user && (
              <div className="text-right">
                <Typography variant="body2" className="font-medium">
                  {user.name}
                </Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  {adAccounts.length} {t('marketing:facebookAds.accounts.count', 'tài khoản')}
                </Typography>
              </div>
            )}

            <FacebookNotificationCenter
              onNotificationClick={(notification) => {
                console.log('Notification clicked:', notification);
                // Handle navigation based on notification type
                if (notification.relatedType === 'campaign' && notification.relatedId) {
                  handleViewCampaign(notification.relatedId.replace('campaign_', ''));
                }
              }}
              onMarkAsRead={(notificationId) => {
                console.log('Mark as read:', notificationId);
              }}
              onMarkAllAsRead={() => {
                console.log('Mark all as read');
              }}
            />

            <FacebookAuthButton
              variant="outline"
              size="sm"
              showUserInfo={false}
            />
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex flex-wrap gap-1 mt-6 border-b">
          <Button
            variant={activeTab === 'overview' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('overview')}
            className="rounded-b-none"
          >
            <Icon name="home" className="mr-2" />
            {t('marketing:facebookAds.tabs.overview', 'Tổng quan')}
          </Button>
          <Button
            variant={activeTab === 'accounts' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('accounts')}
            className="rounded-b-none"
          >
            <Icon name="facebook" className="mr-2" />
            {t('marketing:facebookAds.tabs.accounts', 'Tài khoản')}
          </Button>
          <Button
            variant={activeTab === 'campaigns' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('campaigns')}
            className="rounded-b-none"
          >
            <Icon name="campaign" className="mr-2" />
            {t('marketing:facebookAds.tabs.campaigns', 'Chiến dịch')}
          </Button>
          <Button
            variant={activeTab === 'analytics' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('analytics')}
            className="rounded-b-none"
          >
            <Icon name="bar-chart" className="mr-2" />
            {t('marketing:facebookAds.tabs.analytics', 'Phân tích')}
          </Button>
          <Button
            variant={activeTab === 'budget' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('budget')}
            className="rounded-b-none"
          >
            <Icon name="wallet" className="mr-2" />
            {t('marketing:facebookAds.tabs.budget', 'Ngân sách')}
          </Button>
          <Button
            variant={activeTab === 'monitor' ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab('monitor')}
            className="rounded-b-none"
          >
            <Icon name="activity" className="mr-2" />
            {t('marketing:facebookAds.tabs.monitor', 'Theo dõi')}
          </Button>
        </div>
      </Card>

      {/* Tab Content */}
      <div className="space-y-6">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default FacebookAdsPage;