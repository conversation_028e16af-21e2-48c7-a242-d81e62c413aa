{"title": "外部代理", "subtitle": "管理外部代理", "description": "通过各种协议连接和管理外部代理", "navigation": {"overview": "概览", "agents": "代理列表", "protocols": "协议", "templates": "协议模板", "webhooks": "Webhooks", "analytics": "分析", "messages": "消息", "settings": "设置"}, "agent": {"title": "代理", "name": "代理名称", "description": "描述", "status": "状态", "protocol": "协议", "endpoint": "端点", "capabilities": "功能", "tags": "标签", "created": "创建时间", "updated": "更新时间", "lastConnected": "最后连接", "version": "版本", "isActive": "活跃"}, "status": {"active": "活跃", "inactive": "非活跃", "connecting": "连接中", "error": "错误", "maintenance": "维护中"}, "protocol": {"mcp": "模型上下文协议", "google_agent": "Google代理通信", "rest_api": "REST API", "websocket": "WebSocket", "grpc": "gRPC", "custom": "自定义协议"}, "authentication": {"none": "无认证", "api_key": "API密钥", "bearer_token": "Bearer令牌", "oauth2": "OAuth 2.0", "basic_auth": "基础认证", "custom": "自定义认证"}, "capabilities": {"text_processing": "文本处理", "image_analysis": "图像分析", "data_retrieval": "数据检索", "file_operations": "文件操作", "api_calls": "API调用", "real_time_communication": "实时通信", "custom": "自定义"}, "stats": {"totalAgents": "代理总数", "activeAgents": "活跃代理", "inactiveAgents": "非活跃代理", "errorAgents": "错误代理"}, "actions": {"create": "创建", "edit": "编辑", "delete": "删除", "test": "测试", "connect": "连接", "disconnect": "断开连接", "refresh": "刷新", "export": "导出", "import": "导入", "save": "保存", "cancel": "取消", "confirm": "确认", "retry": "重试"}, "form": {"required": "必填", "optional": "可选", "placeholder": {"name": "输入代理名称", "description": "输入代理描述", "endpoint": "https://api.example.com", "apiKey": "输入API密钥", "token": "输入令牌", "username": "输入用户名", "password": "输入密码", "search": "搜索代理..."}, "validation": {"required": "此字段为必填项", "invalidUrl": "无效的URL", "invalidEmail": "无效的邮箱", "minLength": "最少{min}个字符", "maxLength": "最多{max}个字符"}}, "messages": {"success": {"created": "代理创建成功", "updated": "代理更新成功", "deleted": "代理删除成功", "connected": "连接成功", "tested": "连接测试成功"}, "error": {"createFailed": "创建代理失败", "updateFailed": "更新代理失败", "deleteFailed": "删除代理失败", "connectionFailed": "连接失败", "testFailed": "连接测试失败", "loadFailed": "加载数据失败"}}, "connection": {"test": "测试连接", "testing": "测试中...", "success": "连接成功", "failed": "连接失败", "timeout": "连接超时", "responseTime": "响应时间", "lastTest": "最后测试"}, "performance": {"title": "性能", "totalRequests": "总请求数", "successfulRequests": "成功请求数", "failedRequests": "失败请求数", "averageResponseTime": "平均响应时间", "uptime": "运行时间", "successRate": "成功率"}, "filters": {"all": "全部", "status": "状态", "protocol": "协议", "capabilities": "功能", "tags": "标签", "dateRange": "日期范围", "clear": "清除筛选"}, "pagination": {"previous": "上一页", "next": "下一页", "page": "页", "of": "共", "items": "项", "showing": "显示", "to": "到"}, "empty": {"noAgents": "暂无代理", "noMessages": "暂无消息", "noResults": "未找到结果", "createFirst": "创建您的第一个代理"}, "loading": {"agents": "加载代理中...", "messages": "加载消息中...", "testing": "测试连接中...", "saving": "保存中...", "deleting": "删除中..."}}