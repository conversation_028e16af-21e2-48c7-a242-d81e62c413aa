import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  ResponsiveGrid
} from '@/shared/components/common';
import { useExternalAgents } from '@/modules/external-agents/hooks';
import { ExternalAgentStatus, ProtocolType } from '@/modules/external-agents/types/enums';
import { ExternalAgent } from '@/modules/external-agents/types/externalAgent';

const ExternalAgentIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['common', 'integration', 'external-agents']);
  const navigate = useNavigate();

  // Get external agents data for statistics
  const { data: agentsData } = useExternalAgents({ limit: 100 });

  // Calculate statistics
  const stats = React.useMemo(() => {
    if (!agentsData?.items) {
      return {
        total: 0,
        active: 0,
        inactive: 0,
        error: 0,
        byProtocol: {},
      };
    }

    const byStatus = agentsData.items.reduce((acc: Record<string, number>, agent: ExternalAgent) => {
      acc[agent.status] = (acc[agent.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byProtocol = agentsData.items.reduce((acc: Record<string, number>, agent: ExternalAgent) => {
      acc[agent.protocol] = (acc[agent.protocol] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: agentsData.items.length,
      active: byStatus[ExternalAgentStatus.ACTIVE] || 0,
      inactive: byStatus[ExternalAgentStatus.INACTIVE] || 0,
      error: byStatus[ExternalAgentStatus.ERROR] || 0,
      byProtocol,
    };
  }, [agentsData]);

  const quickActions = [
    {
      title: t('external-agents:actions.create', 'Tạo mới'),
      description: t('integration:externalAgents.createDescription', 'Tạo một external agent mới với cấu hình giao thức tùy chỉnh'),
      icon: 'plus',
      onClick: () => navigate('/external-agents'),
      variant: 'primary' as const,
    },
    {
      title: t('external-agents:navigation.agents', 'Danh sách Agents'),
      description: t('integration:externalAgents.manageDescription', 'Quản lý tất cả external agents hiện có và cài đặt của chúng'),
      icon: 'settings',
      onClick: () => navigate('/external-agents'),
      variant: 'outline' as const,
    },
    {
      title: t('external-agents:navigation.protocols', 'Giao thức'),
      description: t('integration:externalAgents.protocolsDescription', 'Xem và cấu hình các giao thức được hỗ trợ'),
      icon: 'layers',
      onClick: () => navigate('/external-agents/protocols'),
      variant: 'outline' as const,
    },
    {
      title: t('external-agents:navigation.analytics', 'Phân tích'),
      description: t('integration:externalAgents.analyticsDescription', 'Xem phân tích hiệu suất và thống kê sử dụng'),
      icon: 'bar-chart',
      onClick: () => navigate('/external-agents/analytics'),
      variant: 'outline' as const,
    },
  ];

  const protocolStats = Object.entries(stats.byProtocol).map(([protocol, count]) => ({
    label: t(`external-agents:protocol.${protocol}`, protocol),
    value: count,
    color: getProtocolColor(protocol as ProtocolType),
  }));

  function getProtocolColor(protocol: ProtocolType): string {
    switch (protocol) {
      case ProtocolType.MCP:
        return 'purple';
      case ProtocolType.GOOGLE_AGENT:
        return 'blue';
      case ProtocolType.REST_API:
        return 'green';
      case ProtocolType.WEBSOCKET:
        return 'orange';
      case ProtocolType.GRPC:
        return 'indigo';
      default:
        return 'gray';
    }
  }

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h1" className="font-bold mb-2">
          {t('integration:externalAgents.title', 'Quản lý External Agents')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('integration:externalAgents.description', 'Tích hợp với các external agents thông qua MCP, REST API, WebSocket')}
        </Typography>
      </div>

      {/* Statistics Overview */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          {t('integration:externalAgents.overview', 'Tổng quan')}
        </Typography>

        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
          <Card className="p-4">
            <Typography variant="body2" className="text-muted-foreground mb-2">
              {t('external-agents:stats.totalAgents', 'Tổng số agents')}
            </Typography>
            <Typography variant="h2" className="text-blue-600">
              {stats.total}
            </Typography>
          </Card>
          <Card className="p-4">
            <Typography variant="body2" className="text-muted-foreground mb-2">
              {t('external-agents:stats.activeAgents', 'Agents hoạt động')}
            </Typography>
            <Typography variant="h2" className="text-green-600">
              {stats.active}
            </Typography>
          </Card>
          <Card className="p-4">
            <Typography variant="body2" className="text-muted-foreground mb-2">
              {t('external-agents:stats.inactiveAgents', 'Agents không hoạt động')}
            </Typography>
            <Typography variant="h2" className="text-gray-600">
              {stats.inactive}
            </Typography>
          </Card>
          <Card className="p-4">
            <Typography variant="body2" className="text-muted-foreground mb-2">
              {t('external-agents:stats.errorAgents', 'Agents lỗi')}
            </Typography>
            <Typography variant="h2" className="text-red-600">
              {stats.error}
            </Typography>
          </Card>
        </ResponsiveGrid>
      </div>

      {/* Protocol Distribution */}
      {protocolStats.length > 0 && (
        <div className="mb-8">
          <Typography variant="h2" className="mb-4">
            {t('integration:externalAgents.protocolDistribution', 'Phân bố giao thức')}
          </Typography>
          
          <Card>
            <div className="p-6">
              <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3 }}>
                {protocolStats.map((stat, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-md">
                    <Typography variant="body2">{stat.label}</Typography>
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full bg-${stat.color}-500`} />
                      <Typography variant="body2" className="font-medium">
                        {String(stat.value)}
                      </Typography>
                    </div>
                  </div>
                ))}
              </ResponsiveGrid>
            </div>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          {t('integration:externalAgents.quickActions', 'Thao tác nhanh')}
        </Typography>
        
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2 }}>
          {quickActions.map((action, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-primary/10 rounded-lg">
                    <div className="w-6 h-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <Typography variant="h3" className="mb-2">
                      {action.title}
                    </Typography>
                    <Typography variant="body2" className="text-muted-foreground mb-4">
                      {action.description}
                    </Typography>
                    <Button 
                      variant={action.variant}
                      onClick={action.onClick}
                      size="sm"
                    >
                      {action.title}
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Recent Activity */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          {t('integration:externalAgents.recentActivity', 'Hoạt động gần đây')}
        </Typography>

        <Card>
          <div className="p-6">
            <div className="text-center py-8">
              <Typography variant="body1" className="text-muted-foreground">
                {t('integration:externalAgents.noRecentActivity', 'Chưa có hoạt động gần đây')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground mt-1">
                {t('integration:externalAgents.activityWillAppear', 'Hoạt động của external agents sẽ xuất hiện ở đây')}
              </Typography>
            </div>
          </div>
        </Card>
      </div>

      {/* Getting Started */}
      {stats.total === 0 && (
        <div className="mb-8">
          <Card>
            <div className="p-8 text-center">
              <Typography variant="h2" className="mb-4">
                {t('integration:externalAgents.gettingStarted', 'Bắt đầu')}
              </Typography>
              <Typography variant="body1" className="text-muted-foreground mb-6">
                {t('integration:externalAgents.gettingStartedDescription', 'Chưa có external agent nào được cấu hình. Hãy bắt đầu bằng cách tạo agent đầu tiên của bạn.')}
              </Typography>
              <div className="space-y-4">
                <div className="flex items-center gap-3 text-left">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                    1
                  </div>
                  <Typography variant="body2">
                    {t('integration:externalAgents.step1', 'Chọn giao thức tích hợp (MCP, Google Agent, REST API, v.v.)')}
                  </Typography>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                    2
                  </div>
                  <Typography variant="body2">
                    {t('integration:externalAgents.step2', 'Cấu hình endpoint và thông tin xác thực')}
                  </Typography>
                </div>
                <div className="flex items-center gap-3 text-left">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                    3
                  </div>
                  <Typography variant="body2">
                    {t('integration:externalAgents.step3', 'Kiểm tra kết nối và bắt đầu sử dụng')}
                  </Typography>
                </div>
              </div>
              <Button
                variant="primary"
                onClick={() => navigate('/external-agents')}
                className="mt-6"
              >
                {t('external-agents:actions.create', 'Tạo mới')}
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ExternalAgentIntegrationPage;
