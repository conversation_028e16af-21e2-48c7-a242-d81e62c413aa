import { apiClient } from '@/shared/api/axios';
import {
  CreateUrlDto,
  UpdateUrlDto,
  UrlDto,
  FindAllUrlDto,
  PaginatedUrlResult,
  CrawlDto,
  CrawlResultDto,
} from '../types/url.types';

const API_BASE_URL = '/data/url';

/**
 * L<PERSON>y danh sách URL
 * @param queryDto Tham số truy vấn
 * @returns Danh sách URL với phân trang
 */
export const getUrls = async (queryDto?: FindAllUrlDto): Promise<PaginatedUrlResult> => {
  // Xây dựng query string từ queryDto
  const queryParams = new URLSearchParams();

  if (queryDto?.page) queryParams.append('page', queryDto.page.toString());
  if (queryDto?.limit) queryParams.append('limit', queryDto.limit.toString());
  if (queryDto?.keyword) queryParams.append('keyword', queryDto.keyword);
  if (queryDto?.type) queryParams.append('type', queryDto.type);
  if (queryDto?.sortBy) queryParams.append('sortBy', queryDto.sortBy);
  if (queryDto?.sortDirection) queryParams.append('sortDirection', queryDto.sortDirection);

  // Xử lý tags đặc biệt vì có thể là mảng
  if (queryDto?.tags && queryDto.tags.length > 0) {
    queryParams.append('tags', queryDto.tags.join(','));
  }

  const queryString = queryParams.toString();
  const url = queryString ? `${API_BASE_URL}?${queryString}` : API_BASE_URL;

  const response = await apiClient.get<PaginatedUrlResult>(url);
  return response.result;
};

/**
 * Lấy thông tin chi tiết URL
 * @param id ID của URL
 * @returns Thông tin chi tiết URL
 */
export const getUrlDetail = async (id: string): Promise<UrlDto> => {
  const response = await apiClient.get<UrlDto>(`${API_BASE_URL}/${id}`);
  return response.result;
};

/**
 * Tạo URL mới
 * @param dto Thông tin URL cần tạo
 * @returns Thông tin URL đã tạo
 */
export const createUrl = async (dto: CreateUrlDto): Promise<UrlDto> => {
  const response = await apiClient.post<UrlDto>(API_BASE_URL, dto);
  return response.result;
};

/**
 * Cập nhật thông tin URL
 * @param id ID của URL
 * @param dto Thông tin cần cập nhật
 * @returns Thông tin URL đã cập nhật
 */
export const updateUrl = async (id: string, dto: UpdateUrlDto): Promise<UrlDto> => {
  const response = await apiClient.put<UrlDto>(`${API_BASE_URL}/${id}`, dto);
  return response.result;
};

/**
 * Xóa URL
 * @param id ID của URL cần xóa
 * @returns Thông tin về việc xóa URL thành công
 */
export const deleteUrl = async (id: string): Promise<void> => {
  await apiClient.delete(`${API_BASE_URL}/${id}`);
};

/**
 * Xóa nhiều URL cùng lúc
 * @param ids Danh sách ID của các URL cần xóa
 * @returns Thông tin về việc xóa các URL thành công
 */
export const deleteMultipleUrls = async (ids: string[]): Promise<{ success: boolean }> => {
  const response = await apiClient.delete<{ success: boolean }>(`${API_BASE_URL}/batch`, {
    data: { ids },
  });
  return response.result;
};

/**
 * Crawl URL
 * @param dto Thông tin URL cần crawl
 * @returns Kết quả crawl URL
 */
export const crawlUrl = async (dto: CrawlDto): Promise<CrawlResultDto> => {
  const response = await apiClient.post<CrawlResultDto>(`${API_BASE_URL}/crawl/start`, dto);
  return response.result;
};
