import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { PointService } from '../services';
import { CreatePointDto, PointQueryParams, UpdatePointDto } from '../types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { AxiosError } from 'axios';

/**
 * Hook quản lý dữ liệu gói point
 */
export const usePointData = () => {
  const queryClient = useQueryClient();
  const { success, error } = useSmartNotification();

  /**
   * Hook lấy danh sách gói point
   * @param params Tham số truy vấn
   * @returns Danh sách gói point và thông tin phân trang
   */
  const usePoints = (params: PointQueryParams) => {
    return useQuery({
      queryKey: ['points', params],
      queryFn: () => PointService.getPoints(params),
      select: data => data.result,
    });
  };

  /**
   * Hook lấy thông tin chi tiết của một gói point
   * @param id ID của gói point
   * @returns Thông tin chi tiết gói point
   */
  const usePointDetail = (id: number) => {
    return useQuery({
      queryKey: ['point', id],
      queryFn: () => PointService.getPointById(id),
      select: data => data.result,
      enabled: !!id,
    });
  };

  /**
   * Hook tạo mới gói point
   */
  const useCreatePoint = () => {
    return useMutation({
      mutationFn: (data: CreatePointDto) => PointService.createPoint(data),
      onSuccess: (response) => {
        // response là ApiResponseDto<PointDto>, có cấu trúc { code, message, result }
        success({ message: response.message || 'Tạo mới gói point thành công' });
        queryClient.invalidateQueries({ queryKey: ['points'] });
      },
      onError: (err: AxiosError<{ code?: number; message?: string; path?: string; requestId?: string }>) => {
        console.error('Create point error:', err);

        // Xử lý các loại lỗi khác nhau
        let errorMessage = 'Tạo mới gói point thất bại';

        if (err.response?.data) {
          const errorData = err.response.data;

          // Xử lý lỗi 9999 - Unexpected error
          if (errorData.code === 9999) {
            errorMessage = 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại sau.';
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } else if (err.message) {
          errorMessage = err.message;
        }

        error({ message: errorMessage });
      },
    });
  };

  /**
   * Hook cập nhật thông tin gói point
   */
  const useUpdatePoint = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: number; data: UpdatePointDto }) =>
        PointService.updatePoint(id, data),
      onSuccess: (response) => {
        // response là ApiResponseDto<PointDto>, có cấu trúc { code, message, result }
        success({ message: response.message || 'Cập nhật gói point thành công' });
        queryClient.invalidateQueries({ queryKey: ['points'] });
        queryClient.invalidateQueries({ queryKey: ['point'] });
      },
      onError: (err: AxiosError<{ code?: number; message?: string; path?: string; requestId?: string }>) => {
        console.error('Update point error:', err);

        // Xử lý các loại lỗi khác nhau
        let errorMessage = 'Cập nhật gói point thất bại';

        if (err.response?.data) {
          const errorData = err.response.data;

          // Xử lý lỗi 9999 - Unexpected error
          if (errorData.code === 9999) {
            errorMessage = 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại sau.';
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } else if (err.message) {
          errorMessage = err.message;
        }

        error({ message: errorMessage });
      },
    });
  };

  /**
   * Hook xóa gói point
   */
  const useDeletePoint = () => {
    return useMutation({
      mutationFn: (id: number) => PointService.deletePoint(id),
      onSuccess: (response) => {
        // response là ApiResponseDto<{ message: string }>, có cấu trúc { code, message, result }
        success({ message: response.message || 'Xóa gói point thành công' });
        queryClient.invalidateQueries({ queryKey: ['points'] });
      },
      onError: (err: AxiosError<{ code?: number; message?: string; path?: string; requestId?: string }>) => {
        console.error('Delete point error:', err);

        // Xử lý các loại lỗi khác nhau
        let errorMessage = 'Xóa gói point thất bại';

        if (err.response?.data) {
          const errorData = err.response.data;

          // Xử lý lỗi 9999 - Unexpected error
          if (errorData.code === 9999) {
            errorMessage = 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại sau.';
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } else if (err.message) {
          errorMessage = err.message;
        }

        error({ message: errorMessage });
      },
    });
  };

  return {
    usePoints,
    usePointDetail,
    useCreatePoint,
    useUpdatePoint,
    useDeletePoint,
  };
};
