import { z } from 'zod';
import { TFunction } from 'i18next';
import { KnowledgeFileSchema } from './knowledge-file.schema';

/**
 * Schema factory cho vector store với hỗ trợ i18n
 */
export const createVectorStoreSchemas = (t: TFunction) => {
  /**
   * Schema cho vector store
   */
  const VectorStoreSchema = z.object({
    storeId: z.union([z.string(), z.number()]),
    storeName: z.string().min(1, t('admin:data.vectorStore.validation.nameRequired', 'Tên vector store không được để trống')),
    size: z.number().nonnegative(t('admin:data.vectorStore.validation.sizeMustBePositive', 'Dung lượng phải lớn hơn hoặc bằng 0')),
    agents: z.number().int().nonnegative().optional(),
    files: z.number().int().nonnegative().optional(),
    createdAt: z.number(),
    updatedAt: z.number(),
  });

  /**
   * Schema cho tham số truy vấn danh sách vector store
   */
  const VectorStoreQueryParamsSchema = z.object({
    search: z.string().optional(),
    page: z.number().int().positive().optional(),
    limit: z.number().int().positive().optional(),
    sortBy: z.string().optional(),
    sortDirection: z.enum(['asc', 'desc']).optional(),
  });

  /**
   * Schema cho dữ liệu tạo vector store mới
   */
  const CreateVectorStoreSchema = z.object({
    name: z.string().min(1, t('admin:data.vectorStore.validation.nameRequired', 'Tên vector store không được để trống')),
  });

  /**
   * Schema cho dữ liệu gán file vào vector store
   */
  const AssignFilesToVectorStoreSchema = z.object({
    fileIds: z.array(z.string()).min(1, t('admin:data.vectorStore.validation.selectAtLeastOneFile', 'Phải chọn ít nhất một file')),
  });

  return {
    VectorStoreSchema,
    VectorStoreQueryParamsSchema,
    CreateVectorStoreSchema,
    AssignFilesToVectorStoreSchema,
  };
};

// Export legacy schemas for backward compatibility (deprecated)
export const VectorStoreSchema = z.object({
  storeId: z.union([z.string(), z.number()]),
  storeName: z.string().min(1, 'Tên vector store không được để trống'),
  size: z.number().nonnegative('Dung lượng phải lớn hơn hoặc bằng 0'),
  agents: z.number().int().nonnegative().optional(),
  files: z.number().int().nonnegative().optional(),
  createdAt: z.number(),
  updatedAt: z.number(),
});

export const VectorStoreQueryParamsSchema = z.object({
  search: z.string().optional(),
  page: z.number().int().positive().optional(),
  limit: z.number().int().positive().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['asc', 'desc']).optional(),
});

export const CreateVectorStoreSchema = z.object({
  name: z.string().min(1, 'Tên vector store không được để trống'),
});

export const AssignFilesToVectorStoreSchema = z.object({
  fileIds: z.array(z.string()).min(1, 'Phải chọn ít nhất một file'),
});

/**
 * Schema cho phản hồi danh sách vector store
 */
export const VectorStoreListResponseSchema = z.object({
  items: z.array(VectorStoreSchema),
  meta: z.object({
    totalItems: z.number().int().nonnegative(),
    currentPage: z.number().int().positive(),
    itemsPerPage: z.number().int().positive(),
    totalPages: z.number().int().nonnegative(),
  }),
});

/**
 * Schema cho phản hồi danh sách file trong vector store
 */
export const VectorStoreFilesResponseSchema = z.object({
  items: z.array(KnowledgeFileSchema),
  meta: z.object({
    totalItems: z.number().int().nonnegative(),
    currentPage: z.number().int().positive(),
    itemsPerPage: z.number().int().positive(),
    totalPages: z.number().int().nonnegative(),
  }),
});
