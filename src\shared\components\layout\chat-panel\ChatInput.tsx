import { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import ChatInputBox from './ChatInputBox';
import ModernMenu from './ModernMenu';
import { AuthType, useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { useChatKeywords } from '@/modules/settings/hooks/useChatKeywords';
import { adminMenuItems, userMenuItems } from './menu-items';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  onKeywordDetected?: (keyword: string) => void;
  showNotification?: (type: 'success' | 'error' | 'warning' | 'info', message: string) => void;
}

const ChatInput = ({ onSendMessage, onKeywordDetected, showNotification }: ChatInputProps) => {
  const { t } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [chatInputWidth, setChatInputWidth] = useState<number | undefined>(undefined);
  const chatInputRef = useRef<HTMLDivElement>(null);
  const [filterText, setFilterText] = useState<string>('');
  const [isSlashCommand, setIsSlashCommand] = useState<boolean>(false);

  // Lấy chiều rộng của ô chat input để đồng bộ với menu
  useEffect(() => {
    if (chatInputRef.current) {
      const updateWidth = () => {
        if (chatInputRef.current) {
          // Tìm ChatInputBox container
          const chatInputBox = chatInputRef.current.querySelector('.chat-input-box-container');

          if (chatInputBox) {
            // Lấy width chính xác của ChatInputBox
            const boxWidth = chatInputBox.getBoundingClientRect().width;

            // Đảm bảo width tối thiểu
            const minWidth = 300;
            const adjustedWidth = Math.max(boxWidth, minWidth);

            // Chỉ cập nhật khi thay đổi đáng kể
            if (adjustedWidth > 0 && Math.abs(adjustedWidth - (chatInputWidth || 0)) > 5) {
              setChatInputWidth(adjustedWidth);
            }
          }
        }
      };

      // Cập nhật chiều rộng ban đầu
      updateWidth();

      // Cập nhật chiều rộng khi cửa sổ thay đổi kích thước
      window.addEventListener('resize', updateWidth);

      // Lắng nghe sự kiện layout-resized và layout-resizing từ ResizableLayout
      window.addEventListener('layout-resized', updateWidth);
      window.addEventListener('layout-resizing', updateWidth);

      // Tạo một ResizeObserver để theo dõi thay đổi kích thước của container
      const resizeObserver = new ResizeObserver(() => {
        requestAnimationFrame(updateWidth);
      });
      const currentRefForObserver = chatInputRef.current;
      if (currentRefForObserver) {
        resizeObserver.observe(currentRefForObserver);
      }

      // Cập nhật chiều rộng mỗi 100ms để đảm bảo bắt được thay đổi khi kéo thanh resizeable
      const interval = setInterval(updateWidth, 100);

      // Cập nhật chiều rộng khi menu mở
      if (isMenuOpen) {
        updateWidth();
      }

      return () => {
        window.removeEventListener('resize', updateWidth);
        window.removeEventListener('layout-resized', updateWidth);
        window.removeEventListener('layout-resizing', updateWidth);
        if (currentRefForObserver) {
          resizeObserver.unobserve(currentRefForObserver);
        }
        resizeObserver.disconnect();
        clearInterval(interval);
      };
    }

    return undefined;
  }, [chatInputWidth, isMenuOpen]);

  // Áp dụng translation cho menu items
  const menuItems = useAuthCommon().authType == AuthType.ADMIN ? adminMenuItems : userMenuItems;

  const keyword = useChatKeywords(useAuthCommon().authType);
  
  // Kiểm tra từ khóa trong tin nhắn và điều hướng nếu phù hợp
  const checkKeywordsAndNavigate = (message: string): boolean => {
    const lowerMessage = message.toLowerCase().trim();

    // Kiểm tra từng menu item
    for (const item of keyword) {
      // Kiểm tra label
      if (item.label.toLowerCase().includes(lowerMessage)) {
        // Truyền đường dẫn để điều hướng trực tiếp
        onKeywordDetected?.(item.path);
        console.log(`Keyword match found in label: "${message.trim()}" -> ${item.path}`);
        return true;
      }

      // Kiểm tra path (bỏ qua path "/")
      if (item.path.toLowerCase().includes(lowerMessage) && item.path !== '/') {
        // Truyền đường dẫn để điều hướng trực tiếp
        onKeywordDetected?.(item.path);
        console.log(`Keyword match found in path: "${message.trim()}" -> ${item.path}`);
        return true;
      }

      // Kiểm tra keywords
      if (
        item.keywords &&
        item.keywords.some((keyword: string) => {
          const keywordLower = keyword.toLowerCase();
          return lowerMessage.includes(keywordLower) || keywordLower.includes(lowerMessage);
        })
      ) {
        // Truyền đường dẫn để điều hướng trực tiếp
        onKeywordDetected?.(item.path);
        console.log(`Keyword match found in keywords: "${message.trim()}" -> ${item.path}`);
        return true;
      }
    }

    return false;
  };

  // Handle send message with files
  const handleSendMessage = (message: string) => {
    // Nếu đang trong chế độ slash command, đóng menu và reset
    if (isSlashCommand) {
      setIsSlashCommand(false);
      setFilterText('');
      setIsMenuOpen(false);
    }

    // Kiểm tra từ khóa và điều hướng nếu phù hợp
    const navigated = checkKeywordsAndNavigate(message);

    // Nếu không điều hướng, gửi tin nhắn bình thường
    if (!navigated) {
      onSendMessage(message);
    }
  };

  // Xử lý sự kiện nhập liệu để phát hiện lệnh "/"
  const handleInputChange = (text: string) => {
    // Kiểm tra xem có phải là lệnh slash không
    if (text.startsWith('/')) {
      // Nếu chưa mở menu, mở menu lên
      if (!isMenuOpen) {
        setIsMenuOpen(true);
      }

      // Đánh dấu là đang trong chế độ slash command
      setIsSlashCommand(true);

      // Lấy phần text sau dấu "/"
      const searchText = text.substring(1);
      setFilterText(searchText);
    } else if (isSlashCommand) {
      // Nếu không còn bắt đầu bằng "/" nhưng trước đó là slash command
      setIsSlashCommand(false);
      setFilterText('');

      // Đóng menu nếu đang mở
      if (isMenuOpen) {
        setIsMenuOpen(false);
      }
    }
  };

  return (
    <div className="p-3 w-full">
      <div className="relative w-full" ref={chatInputRef}>
        <ChatInputBox
          onSendMessage={handleSendMessage}
          onOpenMenu={() => setIsMenuOpen(true)}
          onInputChange={handleInputChange}
          placeholder={t('chat.typeSlashForMenu')}
          showNotification={showNotification}
        />

        {/* Đặt menu ở ngoài để đảm bảo nó có width đúng */}
        {isMenuOpen && (
          <ModernMenu
            items={menuItems}
            isOpen={isMenuOpen}
            onClose={() => setIsMenuOpen(false)}
            chatInputWidth={chatInputWidth}
            filterText={isSlashCommand ? filterText : ''}
          />
        )}
      </div>
    </div>
  );
};

export default ChatInput;
