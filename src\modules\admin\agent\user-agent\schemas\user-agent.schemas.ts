import { z } from 'zod';
import { TFunction } from 'i18next';
import { UserAgentStatusEnum, UserAgentSortBy, SortDirection } from '../types/user-agent.types';

/**
 * Schema factory cho user agent với hỗ trợ i18n
 */
export const createUserAgentSchemas = (t: TFunction) => {
  const createUserAgentSchema = z.object({
    name: z.string()
      .min(1, t('admin:agent.user.validation.nameRequired', 'Tên agent là bắt buộc'))
      .max(255, t('admin:agent.user.validation.nameMaxLength', 'Tên agent không được quá 255 ký tự')),
    userId: z.string().min(1, t('admin:agent.user.validation.userIdRequired', 'User ID là bắt buộc')),
    avatarMimeType: z.string().optional(),
    description: z.string().optional(),
    instruction: z.string().optional(),
    modelConfig: z.record(z.unknown()),
    status: z.nativeEnum(UserAgentStatusEnum).optional().default(UserAgentStatusEnum.DRAFT),
  });

  return {
    createUserAgentSchema,
  };
};

// Export legacy schema for backward compatibility (deprecated)
export const createUserAgentSchema = z.object({
  name: z.string().min(1, 'Tên agent là bắt buộc').max(255, 'Tên agent không được quá 255 ký tự'),
  userId: z.string().min(1, 'User ID là bắt buộc'),
  avatarMimeType: z.string().optional(),
  description: z.string().optional(),
  instruction: z.string().optional(),
  modelConfig: z.record(z.unknown()),
  status: z.nativeEnum(UserAgentStatusEnum).optional().default(UserAgentStatusEnum.DRAFT),
});

export const updateUserAgentSchema = z.object({
  name: z
    .string()
    .min(1, 'Tên agent là bắt buộc')
    .max(255, 'Tên agent không được quá 255 ký tự')
    .optional(),
  avatarMimeType: z.string().optional(),
  description: z.string().optional(),
  instruction: z.string().optional(),
  modelConfig: z.record(z.unknown()).optional(),
  status: z.nativeEnum(UserAgentStatusEnum).optional(),
});

export const userAgentQuerySchema = z.object({
  page: z.number().min(1).optional().default(1),
  limit: z.number().min(1).max(100).optional().default(10),
  search: z.string().optional(),
  status: z.nativeEnum(UserAgentStatusEnum).optional(),
  userId: z.string().optional(),
  sortBy: z.nativeEnum(UserAgentSortBy).optional().default(UserAgentSortBy.CREATED_AT),
  sortDirection: z.nativeEnum(SortDirection).optional().default(SortDirection.DESC),
});

export type CreateUserAgentFormData = z.infer<typeof createUserAgentSchema>;
export type UpdateUserAgentFormData = z.infer<typeof updateUserAgentSchema>;
export type UserAgentQueryFormData = z.infer<typeof userAgentQuerySchema>;
